'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface ExportActionsDialogProps {
  onExport: (format: string) => void;
}

export function ExportActionsDialog({ onExport }: ExportActionsDialogProps) {
  const [open, setOpen] = useState(false);

  const handleExport = (format: string) => {
    onExport(format);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="bg-vista-dark/40 border-vista-light/20 hover:bg-vista-blue/10 hover:border-vista-blue/30 h-9 sm:h-10 px-3 sm:px-4 text-sm font-medium transition-colors duration-200 w-full sm:w-auto">
          <Download className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
          <span className="truncate">Export Users</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="border border-vista-light/20 bg-vista-dark shadow-xl w-[90vw] max-w-xs sm:max-w-sm rounded-lg overflow-hidden p-0">
        <DialogHeader className="border-b border-vista-light/10 p-4">
          <DialogTitle className="text-vista-light text-lg font-medium">
            Export Users
          </DialogTitle>
        </DialogHeader>
        <div className="p-3">
          <Button
            variant="ghost"
            onClick={() => handleExport('json')}
            className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10 mb-2"
          >
            <span>Export as JSON</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => handleExport('csv')}
            className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
          >
            <span>Export as CSV</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
