import { useState, useEffect, useRef } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { CalendarIcon, ChevronRight } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Bell, Film, Info, SendHorizonal, Tv, Search, Loader2 } from "lucide-react";
import { NotificationType } from "@/models/Notification";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

// Form schema for creating notifications with conditional validation
const notificationFormSchema = z.object({
  type: z.enum(['new_content', 'recommendation', 'update', 'system'] as const),
  title: z.string().max(100),
  message: z.string().max(500),
  contentId: z.string().optional(),
  contentType: z.enum(['movie', 'show'] as const).optional(),
  image: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  expiresAt: z.date().optional(),
}).refine(
  // Ensure both contentId and contentType are provided for 'new_content' and 'recommendation' notifications
  (data) => {
    if (data.type === 'new_content' || data.type === 'recommendation') {
      return !!data.contentId && !!data.contentType;
    }
    return true;
  },
  {
    message: "Content ID and Content Type are required for content-related notifications",
    path: ["contentId"]
  }
);

type FormData = z.infer<typeof notificationFormSchema>;

interface NotificationCreationFormProps {
  onSubmit: (values: FormData) => Promise<void>;
  isSubmitting: boolean;
}

// Interface for API search results
interface SearchResultItem {
  id: string | number;
  title: string;
  type?: string;
  posterPath?: string;
  poster_path?: string;
  backdropPath?: string;
  backdrop_path?: string;
  overview?: string;
  year?: number;
  release_date?: string;
  first_air_date?: string;
}

// Enhanced interface for content items
interface ContentItem {
  id: string;
  title: string;
  type: 'movie' | 'show';
  image?: string;
  overview?: string;
  year?: number;
  posterPath?: string;
  backdropPath?: string;
}

export function NotificationCreationForm({ onSubmit, isSubmitting }: NotificationCreationFormProps) {
  const [showContentFields, setShowContentFields] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<ContentItem[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  // Debug state to track date selection
  const [debugInfo, setDebugInfo] = useState<string>("");

  // Initialize form
  const form = useForm<FormData>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      type: 'system',
      title: '',
      message: '',
      contentId: '',
      contentType: undefined,
      image: '',
      expiresAt: undefined, // Explicitly set as undefined
    },
  });

  // Debug current form values
  const expiresAt = form.watch("expiresAt");
  useEffect(() => {
    console.log("Current expiresAt value:", expiresAt);
  }, [expiresAt]);

  // Watch for type changes to show/hide content fields
  const watchType = form.watch("type");

  // Update showContentFields state when type changes
  useEffect(() => {
    const contentTypes: NotificationType[] = ['new_content', 'recommendation'];
    setShowContentFields(contentTypes.includes(watchType));
  }, [watchType]);

  // Clean up any pending requests on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Format image URL helper
  const getImageUrl = (path: string | undefined | null): string => {
    if (!path || path === 'null' || path === 'undefined' || path.trim() === '') {
      return '';
    }
    
    // If it's already a full URL, return as is
    if (path.startsWith('http')) {
      return path;
    }
    
    // Otherwise, prepend TMDB image path
    return `https://image.tmdb.org/t/p/w500${path.startsWith('/') ? path : `/${path}`}`;
  };

  // Handle content search
  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    console.log(`Search query: "${query}"`);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Don't search if query is too short
    if (!query || query.length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    // Set a timeout to avoid too many requests while typing
    searchTimeoutRef.current = setTimeout(async () => {
      setIsSearching(true);
      setShowSearchResults(true);

      try {
        // Create a new AbortController for this request
        abortControllerRef.current = new AbortController();
        const signal = abortControllerRef.current.signal;

        // Use the main search API endpoint (same as navbar)
        const searchUrl = `/api/search?query=${encodeURIComponent(query)}`;
        console.log(`Fetching from: ${searchUrl}`);

        const response = await fetch(searchUrl, {
          signal,
          headers: {
            'Cache-Control': 'no-cache'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Search API error (${response.status}):`, errorText);
          throw new Error(`Failed to search content: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`Search results:`, data);

        if (data.results) {
          // Map the results to our ContentItem format
          const formattedResults = data.results.map((item: SearchResultItem) => ({
            id: item.id.toString(),
            title: item.title,
            type: item.type === 'movie' ? 'movie' : 'show',
            image: getImageUrl(item.posterPath || item.poster_path),
            overview: item.overview,
            year: item.year || (item.release_date ? new Date(item.release_date).getFullYear() : 
                               item.first_air_date ? new Date(item.first_air_date).getFullYear() : undefined),
            posterPath: item.posterPath || item.poster_path,
            backdropPath: item.backdropPath || item.backdrop_path
          }));

          setSearchResults(formattedResults);
          console.log(`Found ${formattedResults.length} results`);
        } else {
          console.error('Search API returned no results');
          setSearchResults([]);
        }
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error searching content:', error);
        }
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 500);
  };

  // Directly set the expiration date
  const setExpirationDate = (date: Date | undefined) => {
    try {
      if (date) {
        // Make sure the time is set to end of day
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        
        console.log("Setting date:", endOfDay);
        
        // Reset the field first to ensure clean state
        form.resetField("expiresAt");
        
        // Then set the new value
        form.setValue("expiresAt", endOfDay, { 
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true
        });
        
        // Check if the value was set
        setTimeout(() => {
          const currentValue = form.getValues("expiresAt");
          setDebugInfo(`Date set to: ${currentValue ? format(currentValue, "yyyy-MM-dd HH:mm:ss") : "undefined"}`);
          console.log("Form value after set:", currentValue);
        }, 0);
      } else {
        // Call the clear function
        clearDateField();
      }
    } catch (error) {
      console.error("Error setting date:", error);
      setDebugInfo(`Error: ${error}`);
    }
  };

  // Direct method to clear the date field
  const clearDateField = () => {
    console.log("Explicitly clearing date field");
    
    try {
      // Reset the field to undefined
      form.resetField("expiresAt");
      
      // Force re-render
      form.trigger("expiresAt");
      
      // Set debug info
      setDebugInfo("Date cleared successfully");
      console.log("Date field reset, current value:", form.getValues("expiresAt"));
    } catch (error) {
      console.error("Error clearing date:", error);
      setDebugInfo(`Error clearing date: ${error}`);
    }
  };

  // Handle selecting content from search results
  const handleSelectContent = (item: ContentItem) => {
    // Update form values
    form.setValue('contentId', item.id);
    form.setValue('contentType', item.type);
    
    // If the item has an image, use it
    if (item.image) {
      form.setValue('image', item.image);
    }
    // Otherwise, try to construct an image URL from posterPath
    else if (item.posterPath) {
      form.setValue('image', getImageUrl(item.posterPath));
    }

    // If title is empty, suggest using the content title
    if (!form.getValues('title')) {
      form.setValue('title', `New ${item.type === 'movie' ? 'Movie' : 'TV Show'}: ${item.title}`);
    }

    // If message is empty, suggest a default message with overview if available
    if (!form.getValues('message')) {
      let message = `${item.title} is now available to stream on StreamVista.`;
      
      // Add overview if available
      if (item.overview) {
        message += ` ${item.overview.substring(0, 150)}${item.overview.length > 150 ? '...' : ''}`;
      }
      
      form.setValue('message', message);
    }

    // Close search results
    setShowSearchResults(false);
    setSearchQuery('');
  };

  // Handle calendar date click
  const handleCalendarSelect = (date: Date | undefined) => {
    console.log("Calendar date selected:", date);
    
    // Set the date using our helper function
    setExpirationDate(date);
    
    // Close the calendar immediately after selection
    setCalendarOpen(false);
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-4 sm:gap-6 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="col-span-full sm:col-span-1">
                    <FormLabel className="text-vista-light text-sm font-medium">Notification Type</FormLabel>
                    <Select
                      onValueChange={(value: FormData["type"]) => {
                        field.onChange(value);
                        const contentTypes: NotificationType[] = ['new_content', 'recommendation'];
                        setShowContentFields(contentTypes.includes(value));
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-vista-dark border-vista-light/20 h-11 text-vista-light">
                          <SelectValue placeholder="Select notification type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-vista-dark-lighter border-vista-light/20">
                        <SelectItem value="system" className="flex items-center gap-2">
                          <div className="flex items-center gap-2">
                            <Bell className="h-4 w-4 text-purple-400" />
                            <span>System</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="update">
                          <div className="flex items-center gap-2">
                            <Info className="h-4 w-4 text-amber-400" />
                            <span>Update</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="new_content">
                          <div className="flex items-center gap-2">
                            <Film className="h-4 w-4 text-vista-blue" />
                            <span>New Content</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="recommendation">
                          <div className="flex items-center gap-2">
                            <Tv className="h-4 w-4 text-green-400" />
                            <span>Recommendation</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription className="admin-caption-mobile">
                      Type of notification to send to users
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiresAt"
                render={({ field }) => (
                  <FormItem className="flex flex-col col-span-full sm:col-span-1">
                    <FormLabel className="text-vista-light text-sm font-medium">Expiration Date (Optional)</FormLabel>
                    <FormControl>
                      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-medium bg-vista-dark border border-vista-light/20 rounded-lg h-11 text-vista-light",
                              !field.value && "text-vista-light/50",
                              field.value && "text-vista-blue border-vista-blue/30 bg-vista-blue/5"
                            )}
                          >
                            {field.value ? (
                              <span className="flex items-center">
                                <CalendarIcon className="mr-2 h-4 w-4 text-vista-blue flex-shrink-0" />
                                <span className="truncate">{format(field.value, "MMMM d, yyyy")}</span>
                              </span>
                            ) : (
                              <span className="flex items-center text-vista-light/50">
                                <CalendarIcon className="mr-2 h-4 w-4 opacity-70 flex-shrink-0" />
                                <span className="truncate">Pick a date</span>
                              </span>
                            )}
                            <ChevronRight className="ml-auto h-4 w-4 opacity-50 flex-shrink-0" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0 bg-vista-dark-lighter border border-vista-light/20 rounded-xl shadow-lg shadow-vista-blue/10 mx-4 sm:mx-0"
                          align="start"
                          sideOffset={5}
                        >
                          <div className="p-2 sm:p-3 bg-gradient-to-b from-vista-dark to-vista-dark-lighter rounded-xl">
                            <div className="flex justify-between items-center mb-2">
                              <h4 className="admin-text-mobile font-medium text-vista-light">Select Expiration Date</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 sm:h-7 sm:w-7 p-0 text-vista-light/70 hover:text-vista-light admin-focus-mobile"
                                onClick={() => setCalendarOpen(false)}
                              >
                                ✕
                              </Button>
                            </div>

                            {/* Mobile-optimized Calendar */}
                            <div className="pointer-events-auto cursor-pointer select-none">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={handleCalendarSelect}
                                disabled={(date) => {
                                  // Ensure date is not in the past
                                  const today = new Date();
                                  today.setHours(0, 0, 0, 0);
                                  return date < today;
                                }}
                                className="bg-transparent text-vista-light [&_.rdp-button]:cursor-pointer [&_.rdp-day]:pointer-events-auto [&_.rdp-day]:cursor-pointer [&_.rdp-nav_button]:cursor-pointer text-sm sm:text-base"
                                showOutsideDays={true}
                                initialFocus
                              />
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormDescription className="admin-caption-mobile">
                      When this notification should expire (optional)
                    </FormDescription>
                    <FormMessage />
                    {debugInfo && (
                      <div className="mt-2 admin-caption-mobile text-vista-blue">{debugInfo}</div>
                    )}
                    <div className="mt-2 flex justify-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="admin-caption-mobile bg-red-500/10 text-red-400 hover:bg-red-500/20 admin-focus-mobile"
                        onClick={() => {
                          clearDateField();
                          setDebugInfo("Date cleared");
                        }}
                      >
                        Clear Date
                      </Button>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-vista-light text-sm font-medium">Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Notification title"
                      className="bg-vista-dark border-vista-light/20 h-11 text-vista-light"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="text-xs text-vista-light/60">
                    A clear, concise title for the notification
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-vista-light text-sm font-medium">Message</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Notification message"
                      className="min-h-[100px] bg-vista-dark border-vista-light/20 text-vista-light resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="text-xs text-vista-light/60">
                    Detailed message to display in the notification
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {showContentFields && (
              <div className="admin-spacing-mobile sm:space-y-6 rounded-md bg-vista-dark/40 p-3 sm:p-4 border border-vista-light/10">
                <h3 className="admin-text-mobile sm:text-sm font-medium text-vista-light flex items-center gap-2">
                  <Film className="h-4 w-4 text-vista-blue flex-shrink-0" />
                  <span className="truncate">Content Reference (Required)</span>
                </h3>

                {/* Mobile-optimized content search field */}
                <div className="space-y-2">
                  <FormLabel className="text-vista-light text-sm font-medium">Search Content</FormLabel>
                  <div className="relative">
                    <div className="flex items-center border border-vista-light/20 rounded-lg overflow-hidden bg-vista-dark h-11">
                      <Search className="h-4 w-4 ml-3 text-vista-light/50 flex-shrink-0" />
                      <input
                        type="text"
                        placeholder="Search movies, shows, or content by title..."
                        className="flex-1 bg-transparent border-none focus:outline-none focus:ring-0 py-3 px-3 text-vista-light placeholder:text-vista-light/50 min-w-0 text-sm"
                        value={searchQuery}
                        onChange={(e) => handleSearch(e.target.value)}
                        onFocus={() => setShowSearchResults(!!searchQuery && searchQuery.length >= 2)}
                      />
                      {isSearching && <Loader2 className="h-4 w-4 mr-3 text-vista-light/50 animate-spin" />}
                    </div>

                    {/* Search results dropdown */}
                    {showSearchResults && (
                      <div className="absolute mt-1 w-full z-10 bg-vista-dark-lighter border border-vista-light/20 shadow-lg rounded-md overflow-hidden">
                        <div className="max-h-56 overflow-auto">
                          {isSearching ? (
                            <div className="p-4 text-center text-vista-light/70">
                              <Loader2 className="h-5 w-5 mx-auto mb-2 animate-spin text-vista-blue" />
                              Searching...
                            </div>
                          ) : searchResults.length === 0 ? (
                            <div className="p-4 text-center text-vista-light/70">
                              No results found
                            </div>
                          ) : (
                            <div className="py-2">
                              {searchResults.map((item) => (
                                <div
                                  key={`${item.type}-${item.id}`}
                                  className="px-3 py-2 hover:bg-vista-dark/50 cursor-pointer flex items-center gap-2"
                                  onClick={() => handleSelectContent(item)}
                                >
                                  {item.image && (
                                    <div className="h-10 w-10 rounded overflow-hidden flex-shrink-0">
                                      <Image
                                        src={item.image}
                                        alt={item.title}
                                        width={40}
                                        height={40}
                                        className="h-full w-full object-cover"
                                      />
                                    </div>
                                  )}
                                  <div className="flex-1 min-w-0">
                                    <div className="text-sm font-medium text-vista-light truncate">{item.title}</div>
                                    <div className="flex items-center gap-2">
                                      <Badge variant="outline" className="text-xs">
                                        {item.type === 'movie' ? 'Movie' : 'TV Show'}
                                      </Badge>
                                      {item.year && (
                                        <span className="text-xs text-vista-light/70">{item.year}</span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  <FormDescription>
                    Search for content by title to auto-fill content details
                  </FormDescription>
                </div>

                <div className="grid gap-4 sm:gap-6 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="contentType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-vista-light text-sm font-medium">Content Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-vista-dark border-vista-light/20 h-11 text-vista-light">
                              <SelectValue placeholder="Select content type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-vista-dark-lighter border-vista-light/20">
                            <SelectItem value="movie">Movie</SelectItem>
                            <SelectItem value="show">TV Show</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contentId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-vista-light text-sm font-medium">Content ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter content ID"
                            className="bg-vista-dark border-vista-light/20 h-11 text-vista-light"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-vista-light text-sm font-medium">Image URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://example.com/image.jpg"
                          className="bg-vista-dark border-vista-light/20 h-11 text-vista-light"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-xs text-vista-light/60">
                        URL to an image related to this notification
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-vista-blue hover:bg-vista-blue/90 w-full sm:w-auto"
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <SendHorizonal className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Send Notification</span>
                  <span className="sm:hidden">Send</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}