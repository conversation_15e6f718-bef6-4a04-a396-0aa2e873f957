'use client';

import { useMemo, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Search, Check, SlidersHorizontal } from 'lucide-react';

interface FilterOption {
  id: string;
  name: string;
}

// Add sort options
interface SortOption {
  id: string; // e.g., 'popularity.desc'
  name: string; // e.g., 'Popularity Descending'
}

interface FilterPanelProps {
  availableGenres: string[];
  yearFilters: FilterOption[];
  ratingFilters: FilterOption[];
  sortOptions: SortOption[]; // Add sort options
  selectedGenres: string[];
  selectedYear: string;
  selectedRating: string;
  selectedSortBy: string; // Add selected sort
  onGenreToggle: (genre: string) => void;
  onYearChange: (yearId: string) => void;
  onRatingChange: (ratingId: string) => void;
  onSortChange: (sortId: string) => void; // Add sort handler
  filterType: 'movie' | 'show';
}

export function FilterPanel({
  availableGenres,
  yearFilters,
  ratingFilters,
  sortOptions,
  selectedGenres,
  selectedYear,
  selectedRating,
  selectedSortBy,
  onGenreToggle,
  onYearChange,
  onRatingChange,
  onSortChange,
  filterType = 'movie',
}: FilterPanelProps) {
  const yearLabel = filterType === 'show' ? 'First Air Year' : 'Release Year';
  const [genreQuery, setGenreQuery] = useState('');
  
  // Enhanced memoization for genre filtering
  const filteredGenres = useMemo(() => {
    if (!genreQuery.trim()) return availableGenres;
    const q = genreQuery.toLowerCase();
    return availableGenres.filter(g => g.toLowerCase().includes(q));
  }, [availableGenres, genreQuery]);

  return (
    <Card className="w-full bg-gradient-to-b from-vista-dark-lighter/95 to-vista-dark-lighter/75 backdrop-blur-sm border border-vista-light/15 rounded-2xl shadow-lg overflow-hidden">
      <CardContent className="p-0">
        <div className="bg-vista-blue/5 border-b border-vista-light/10 p-4 md:p-5">
          <h2 className="text-sm font-medium text-vista-light flex items-center">
            <SlidersHorizontal className="h-4 w-4 mr-2 text-vista-blue" />
            Filter Options
          </h2>
        </div>

        <div className="p-5 md:p-6 space-y-6">
          {/* Genres Section */}
          <div className="space-y-3">
            <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-blue/80 flex items-center">
              <span className="w-1.5 h-1.5 rounded-full bg-vista-blue mr-2"></span>
              Genres
            </h3>
            <div className="relative group">
              <Input
                value={genreQuery}
                onChange={(e) => setGenreQuery(e.target.value)}
                placeholder="Search genres"
                className="pl-9 h-9 text-xs bg-vista-dark/80 border-vista-light/20 text-vista-light/90 placeholder:text-vista-light/40 rounded-lg transition-all duration-200 focus:ring-1 focus:ring-offset-0 focus:ring-vista-blue/40 focus:bg-vista-dark focus:border-vista-blue/30"
              />
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-vista-light/40 group-focus-within:text-vista-blue/70 transition-colors duration-200" />
            </div>
            
            <div className="mt-3 flex flex-wrap gap-2 max-h-[140px] overflow-y-auto pr-1 custom-scrollbar">
              {filteredGenres.map((genre) => {
                const selected = selectedGenres.includes(genre);
                return (
                  <Badge
                    key={genre}
                    variant={selected ? 'default' : 'outline'}
                    aria-pressed={selected}
                    className={`cursor-pointer select-none transition-all duration-200 text-xs px-3 py-1.5 rounded-full border shadow-sm ${
                      selected
                        ? 'bg-vista-blue/20 text-vista-blue border-vista-blue/50 hover:bg-vista-blue/30 transform hover:-translate-y-0.5'
                        : 'border-vista-light/15 bg-vista-dark/40 text-vista-light/70 hover:border-vista-light/40 hover:bg-vista-light/10 hover:-translate-y-0.5'
                    }`}
                    onClick={() => onGenreToggle(genre)}
                  >
                    {selected && (
                      <span className="mr-1.5 flex items-center justify-center bg-vista-blue/30 rounded-full w-3.5 h-3.5">
                        <Check className="h-2.5 w-2.5" />
                      </span>
                    )}
                    {genre}
                  </Badge>
                );
              })}
              {filteredGenres.length === 0 && (
                <div className="w-full flex items-center justify-center py-4 text-xs text-vista-light/50">
                  No genres match "{genreQuery}".
                </div>
              )}
            </div>
          </div>

          {/* Filters Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Year Filter */}
            <div className="space-y-2.5">
              <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-blue/80 flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-vista-blue mr-2"></span>
                {yearLabel}
              </h3>
              <Select value={selectedYear} onValueChange={onYearChange}>
                <SelectTrigger className="w-full bg-vista-dark/80 border-vista-light/20 text-vista-light focus:ring-1 focus:ring-offset-0 focus:ring-vista-blue/40 focus:border-vista-blue/30 text-xs h-10 rounded-lg transition-all duration-200">
                  <SelectValue placeholder={`Select ${yearLabel}`} />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light rounded-lg">
                  {yearFilters.map((option) => (
                    <SelectItem 
                      key={option.id} 
                      value={option.id} 
                      className="focus:bg-vista-blue/20 text-xs hover:bg-vista-light/10 rounded"
                    >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Rating Filter */}
            <div className="space-y-2.5">
              <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-blue/80 flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-vista-blue mr-2"></span>
                Rating
              </h3>
              <Select value={selectedRating} onValueChange={onRatingChange}>
                <SelectTrigger className="w-full bg-vista-dark/80 border-vista-light/20 text-vista-light focus:ring-1 focus:ring-offset-0 focus:ring-vista-blue/40 focus:border-vista-blue/30 text-xs h-10 rounded-lg transition-all duration-200">
                  <SelectValue placeholder="Select Rating" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light rounded-lg">
                  {ratingFilters.map((option) => (
                    <SelectItem 
                      key={option.id} 
                      value={option.id} 
                      className="focus:bg-vista-blue/20 text-xs hover:bg-vista-light/10 rounded"
                    >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="space-y-2.5">
              <h3 className="text-xs font-semibold uppercase tracking-wide text-vista-blue/80 flex items-center">
                <span className="w-1.5 h-1.5 rounded-full bg-vista-blue mr-2"></span>
                Sort By
              </h3>
              <Select value={selectedSortBy} onValueChange={onSortChange}>
                <SelectTrigger className="w-full bg-vista-dark/80 border-vista-light/20 text-vista-light focus:ring-1 focus:ring-offset-0 focus:ring-vista-blue/40 focus:border-vista-blue/30 text-xs h-10 rounded-lg transition-all duration-200">
                  <SelectValue placeholder="Select sorting" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20 text-vista-light rounded-lg">
                  {sortOptions.map((option) => (
                    <SelectItem 
                      key={option.id} 
                      value={option.id} 
                      className="focus:bg-vista-blue/20 text-xs hover:bg-vista-light/10 rounded"
                    >
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 