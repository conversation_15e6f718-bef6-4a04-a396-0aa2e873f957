import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import crypto from 'crypto';
import { isAdmin } from '@/lib/middleware';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import UserActivity from '@/models/UserActivity';
import { IUserActivity } from '@/models/UserActivity';

// Admin APIs don't need rate limiting
// Rate limiting code has been removed to prevent errors for admin users

// Input validation types
interface QueryParams {
  limit: number;
  page: number;
  type?: string;
  userId?: string;
}

// Response types
interface PaginationData {
  total: number;
  limit: number;
  page: number;
  pages: number;
}

interface ActivityResponse {
  logs: FormattedLog[];
  pagination: PaginationData;
}

interface FormattedLog {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  type: string;
  action: string;
  details: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, unknown>;
  user?: {
    name: string;
    email: string;
    profileImage: string;
  };
}

// Interface for aggregation result document
interface AggregatedActivityDoc {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  type: string;
  action: string;
  details: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, unknown>;
  userInfo?: {
    _id: mongoose.Types.ObjectId;
    name: string;
    email: string;
    image?: string;
    profileImage?: string;
    avatar?: string;
  };
}

/**
 * Validates and parses query parameters
 */
function parseQueryParams(searchParams: URLSearchParams): QueryParams {
  // Parse limit with default value and constraints
  const limitParam = searchParams.get('limit');
  const limit = limitParam ? parseInt(limitParam, 10) : 50;
  const safeLimit = Math.min(Math.max(limit, 1), 100); // Constrain between 1 and 100

  // Parse page with default value
  const pageParam = searchParams.get('page');
  const page = pageParam ? Math.max(parseInt(pageParam, 10), 1) : 1;

  // Get optional filters
  const type = searchParams.get('type') || undefined;
  const userId = searchParams.get('userId') || undefined;

  return {
    limit: safeLimit,
    page,
    type,
    userId
  };
}

// Rate limiting has been removed for admin APIs

/**
 * GET /api/admin/activity
 * Get all user activity logs with pagination
 */
export async function GET(request: NextRequest) {
  // Admin APIs don't use rate limiting to prevent errors

  try {
    // Add request ID for tracing
    const requestId = crypto.randomBytes(8).toString('hex');

    // Step 1: Admin authentication with fallback pattern
    console.log(`[${requestId}] Admin activity API: Authentication check starting`);

    // Get userId from query parameters as fallback (same pattern as other working admin routes)
    const { searchParams } = new URL(request.url);
    const queryUserId = searchParams.get('userId');

    const adminCheck = await isAdmin(request);
    let adminUserId: string;

    // If cookie auth fails but we have a userId in the query, verify admin status directly
    if (!adminCheck.isAuthorized && queryUserId) {
      console.log(`[${requestId}] Cookie-based auth failed, trying query param userId:`, queryUserId);

      // Connect to database for direct verification
      await ensureMongooseConnection();

      // Find the user and check if they're an admin
      const user = await User.findById(queryUserId).select('role').lean();

      if (!user) {
        console.error(`[${requestId}] User not found for query param userId:`, queryUserId);
        return NextResponse.json({
          error: 'User not found',
          logs: [],
          pagination: { total: 0, limit: 50, page: 1, pages: 0 }
        }, { status: 404 });
      }

      if ((user as any).role !== 'admin' && (user as any).role !== 'superadmin') {
        console.error(`[${requestId}] User does not have admin role:`, queryUserId);
        return NextResponse.json({
          error: 'Forbidden: You do not have permission to access this resource',
          logs: [],
          pagination: { total: 0, limit: 50, page: 1, pages: 0 }
        }, { status: 403 });
      }

      // User is authenticated and is an admin
      console.log(`[${requestId}] User ${queryUserId} verified as admin via query param`);
      adminUserId = queryUserId;
    } else if (!adminCheck.isAuthorized) {
      // Both cookie auth and query param auth failed
      console.error(`[${requestId}] Admin activity API: Auth failed -`, adminCheck.message);
      return NextResponse.json({
        error: 'Unauthorized access',
        message: adminCheck.message || 'You do not have permission to access this resource',
        logs: [],
        pagination: { total: 0, limit: 50, page: 1, pages: 0 }
      }, { status: 403 });
    } else {
      // Cookie auth succeeded
      adminUserId = adminCheck.user?._id?.toString() || 'unknown-id';
      console.log(`[${requestId}] User ${adminUserId} verified as admin via cookie`);
    }

    // Step 2: Parse and validate query parameters (searchParams already parsed above)
    const { limit, page, type, userId } = parseQueryParams(searchParams);
    const skip = (page - 1) * limit;

    // Check for cache busting timestamp and remove it for caching purposes
    const timestamp = searchParams.get('_t');
    if (timestamp) {
      console.log(`[${requestId}] Admin activity API: Ignoring cache busting timestamp ${timestamp}`);
    }

    // Step 3: Connect to database
    console.log(`[${requestId}] Admin activity API: Connecting to database`);
    await ensureMongooseConnection();

    // Step 4: Build query
    const query: mongoose.FilterQuery<IUserActivity> = {};
    if (type) query.type = type;
    if (userId) {
      try {
        query.userId = new mongoose.Types.ObjectId(userId);
      } catch (error) {
        // If userId is invalid, return an error
        return NextResponse.json({
          error: 'Invalid userId format',
          logs: [],
          pagination: { total: 0, limit, page, pages: 0 }
        }, { status: 400 });
      }
    }

    console.log(`[${requestId}] Admin activity API: Executing query with params:`, { limit, page, type, userId });

    // Step 5: Execute query efficiently using aggregation
    // Using type assertion to bypass strict typing issues
    const aggregationPipeline = [
      { $match: query },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [
            { $sort: { timestamp: -1 } },
            { $skip: skip },
            { $limit: limit },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'userInfo',
                pipeline: [
                  { $project: {
                    name: 1,
                    email: 1,
                    image: 1,
                    profileImage: 1,
                    avatar: 1
                  }}
                ]
              }
            },
            { $unwind: { path: '$userInfo', preserveNullAndEmptyArrays: true } }
          ]
        }
      }
    ] as mongoose.PipelineStage[];

    const results = await UserActivity.aggregate(aggregationPipeline);

    // Step 6: Process and format the results
    const logs = results[0]?.data || [];
    const total = results[0]?.metadata[0]?.total || 0;

    console.log(`[${requestId}] Admin activity API: Found ${logs.length} logs (total: ${total})`);

    const formattedLogs: FormattedLog[] = logs.map((log: AggregatedActivityDoc) => {
      // Determine the best user profile image to use
      const profileImage =
        log.userInfo?.profileImage ||
        log.userInfo?.image ||
        log.userInfo?.avatar ||
        '';

      return {
        id: log._id.toString(),
        userId: log.userId.toString(),
        userName: log.userInfo?.name || 'Unknown',
        userEmail: log.userInfo?.email || '',
        type: log.type,
        action: log.action,
        details: log.details,
        timestamp: log.timestamp,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        metadata: log.metadata || {},
        user: log.userInfo ? {
          name: log.userInfo.name,
          email: log.userInfo.email,
          profileImage
        } : undefined
      };
    });

    // Step 7: Prepare the response data
    const responseData: ActivityResponse = {
      logs: formattedLogs,
      pagination: {
        total,
        limit,
        page,
        pages: Math.ceil(total / limit)
      }
    };

    // Step 8: Create response with appropriate headers
    const response = NextResponse.json(responseData);

    // Admin data should not be cached by browsers
    response.headers.set('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    // Add ETag for conditional requests
    const etag = crypto
      .createHash('md5')
      .update(JSON.stringify(responseData))
      .digest('hex');
    response.headers.set('ETag', `"${etag}"`);

    response.headers.set('Last-Modified', new Date().toUTCString());

    console.log(`[${requestId}] Admin activity API: Request completed successfully`);

    return response;
  } catch (error) {
    // Log error details but don't expose them in the response
    console.error('Admin activity API error:', error instanceof Error ? error.message : 'Unknown error', error);

    // Check for specific error types
    if (
        error instanceof mongoose.mongo.MongoServerSelectionError ||
        (error instanceof Error &&
        (error.message.includes('ECONNREFUSED') ||
         error.message.includes('MongoNetworkError')))) {
      return NextResponse.json({
        error: 'Database connection error',
        message: 'Unable to connect to the database. Please try again later.',
        logs: [],
        pagination: { total: 0, limit: 50, page: 1, pages: 0 }
      }, { status: 503 });
    }

    // Generic error response
    return NextResponse.json({
      error: 'An error occurred while fetching activity logs',
      message: 'Please try again later or contact support if the problem persists.',
      logs: [],
      pagination: { total: 0, limit: 50, page: 1, pages: 0 }
    }, { status: 500 });
  }
}
