'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Bell,
  RefreshCw,
  AlertTriangle,
  Trash2,
  Plus,
  Clock
} from 'lucide-react';
import { NotificationType } from '@/models/Notification';
import { NotificationCard } from '@/components/admin/NotificationCard';
import { NotificationCreationForm } from '@/components/admin/NotificationCreationForm';
import { NotificationsStats } from '@/components/admin/NotificationsStats';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';

// Define the notification interface
interface Notification {
  _id: string;
  type: NotificationType;
  title: string;
  message: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
  image?: string;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  userId?: string;
  recipientCount?: number;
}

// Define notification form values interface
interface NotificationFormValues {
  type: NotificationType;
  title: string;
  message: string;
  contentId?: string;
  contentType?: 'movie' | 'show';
  image?: string;
  expiresAt?: Date | string;
  userId?: string;
}

// No longer needed as we're using a simple string for error state

export default function AdminNotificationsClient() {
  const [activeTab, setActiveTab] = useState('all');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletingIds, setDeletingIds] = useState<string[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const { user, isAuthenticated, isAdmin } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  // Access control check
  useEffect(() => {
    if (isAuthenticated && !isAdmin()) {
      router.push('/');
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
    }
  }, [isAuthenticated, isAdmin, router, toast]);

  // Initial fetch without using fetchNotifications in dependencies
  useEffect(() => {
    if (isAuthenticated && isAdmin() && user?.id) {
      // Define function inside the effect to avoid dependency issues
      const loadNotifications = async () => {
        setLoading(true);
        setError(null);

        try {
          const response = await fetch(`/api/admin/notifications?userId=${user.id}`, {
            headers: {
              'Authorization': `Bearer ${user.id}`,
              'Cache-Control': 'no-cache'
            },
            cache: 'no-store'
          });

          if (!response.ok) {
            throw new Error('Failed to fetch notifications');
          }

          const data = await response.json();
          setNotifications(data.notifications || []);
        } catch (err) {
          console.error('Error fetching notifications:', err);
          setError('Failed to load notifications. Please try again.');
        } finally {
          setLoading(false);
        }
      };

      loadNotifications();
    }
  }, [isAuthenticated, isAdmin, user]);

  // Separate fetchNotifications function for manual refreshes
  const fetchNotifications = useCallback(async (forceRefresh = true) => {
    if (!user?.id) {
      setError('User ID not available');
      return;
    }

    // Clear existing notifications if forcing a refresh
    if (forceRefresh) {
      setNotifications([]);
    }

    setLoading(true);
    setError(null);

    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/notifications?userId=${user.id}&t=${timestamp}`, {
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      console.log('Fetched notifications:', data.notifications);
      setNotifications(data.notifications || []);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Failed to load notifications. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Handle form submission
  const onSubmit = async (values: NotificationFormValues) => {
    setIsSubmitting(true);

    try {
      // Include userId in the request
      if (!user?.id) {
        throw new Error('User ID not available');
      }

      // Format the expiresAt date if it exists
      if (values.expiresAt && values.expiresAt instanceof Date) {
        // Set the time to end of day (23:59:59) to ensure it expires at the end of the selected day
        const date = new Date(values.expiresAt);
        date.setHours(23, 59, 59, 999);
        values.expiresAt = date.toISOString();
        console.log('Formatted expiration date:', values.expiresAt);
      }

      // Add userId to values
      values.userId = user.id;
      
      // Debug: Log the notification data being sent
      console.log('Sending notification data:', JSON.stringify(values, null, 2));
      
      // Validate content notification fields
      if (values.type === 'new_content' || values.type === 'recommendation') {
        console.log('Content notification validation:');
        console.log('- contentId:', values.contentId);
        console.log('- contentType:', values.contentType);
        
        if (!values.contentId) {
          throw new Error('Content ID is required for content notifications');
        }
        
        if (!values.contentType || !['movie', 'show'].includes(values.contentType)) {
          throw new Error('Valid content type (movie or show) is required for content notifications');
        }
      }

      const response = await fetch(`/api/admin/notifications/broadcast?userId=${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.id}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(values),
        cache: 'no-store'
      });

      // Debug: Log the response status
      console.log('Notification API response status:', response.status);
      
      const responseText = await response.text();
      console.log('Notification API response text:', responseText);
      
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing response JSON:', e);
        throw new Error('Invalid response from server');
      }

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to send notification');
      }

      toast({
        title: 'Notification Sent',
        description: `Successfully sent to ${responseData.sentCount} users`,
        variant: 'default',
      });

      // Reset form and hide it
      setShowCreateForm(false);

      // Refresh notifications list
      await fetchNotifications();
    } catch (err: unknown) {
      console.error('Error sending notification:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send notification';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete all notifications
  const handleDeleteAllNotifications = async () => {
    setIsDeleting(true);

    try {
      // Include userId in the request
      if (!user?.id) {
        throw new Error('User ID not available');
      }

      const response = await fetch(`/api/admin/notifications?userId=${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ userId: user.id }), // Include userId in the body as well
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete notifications');
      }

      const result = await response.json();
      console.log('Delete all result:', result);

      // Refresh notifications list by re-fetching with a delay
      setTimeout(async () => {
        await fetchNotifications(true);
      }, 500);
    } catch (err: unknown) {
      console.error('Error deleting notifications:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete notifications';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle delete individual notification
  const handleDeleteNotification = async (id: string) => {
    try {
      // Include userId in the request
      if (!user?.id) {
        throw new Error('User ID not available');
      }

      // Set a loading state for this specific notification
      setDeletingIds(prev => [...prev, id]);

      // Remove from UI immediately (optimistic update)
      setNotifications(prev => prev.filter(n => n._id !== id));

      console.log(`Deleting notification ${id} with userId ${user.id}`);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/notifications/${id}?userId=${user.id}&t=${timestamp}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify({
          userId: user.id,
          notificationId: id // Explicitly include the notification ID in the body
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error response:', errorData);
        throw new Error(errorData.error || 'Failed to delete notification');
      }

      const result = await response.json();
      console.log('Deletion result:', result);

      // Don't immediately fetch notifications - wait a moment to ensure the deletion is processed
      // This helps prevent the notification from reappearing due to race conditions
      setTimeout(async () => {
        await fetchNotifications(true);
      }, 500);
    } catch (err: unknown) {
      console.error('Error deleting notification:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete notification';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      // Refresh the list to restore state in case of error, with a delay
      setTimeout(async () => {
        await fetchNotifications(true);
      }, 500);
    } finally {
      // Remove this ID from the loading state
      setDeletingIds(prev => prev.filter(itemId => itemId !== id));
    }
  };

  // Handle cleanup of expired notifications
  const handleCleanupExpiredNotifications = async () => {
    setIsCleaningUp(true);

    try {
      // Include userId in the request
      if (!user?.id) {
        throw new Error('User ID not available');
      }

      const response = await fetch(`/api/admin/cleanup/notifications`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.id}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ userId: user.id }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to clean up expired notifications');
      }

      const result = await response.json();

      toast({
        title: 'Cleanup Complete',
        description: `Successfully removed ${result.deletedCount} expired notifications`,
        variant: 'default',
      });

      // Refresh notifications list
      await fetchNotifications(true);
    } catch (err: unknown) {
      console.error('Error cleaning up expired notifications:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to clean up expired notifications';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsCleaningUp(false);
    }
  };

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    return notification.type === activeTab;
  });

  return (
    <div className="admin-mobile-container">
      {/* Mobile-optimized header */}
      <div className="bg-vista-dark-lighter p-4 sm:p-5 rounded-lg border border-vista-light/10 shadow-md">
        <div className="flex flex-col space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="admin-title-mobile sm:text-2xl font-bold text-vista-light flex items-center gap-2 sm:gap-3">
                <div className="bg-vista-dark p-1.5 sm:p-2 rounded-full shadow-sm flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0">
                  <Bell className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
                </div>
                <span className="truncate">Notifications Management</span>
              </h1>
              <p className="admin-subtitle-mobile mt-1 ml-0 sm:ml-1">
                Send and manage system notifications for all users
              </p>
            </div>
          </div>

          {/* Mobile-optimized action buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchNotifications(true)}
              className="text-vista-light border-vista-light/10 hover:bg-vista-dark hover:text-vista-light h-9 admin-focus-mobile"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              size="sm"
              className="bg-vista-blue/80 hover:bg-vista-blue/90 text-white h-9 admin-focus-mobile"
              onClick={() => setShowCreateForm(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">New Notification</span>
              <span className="sm:hidden">Create</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCleanupExpiredNotifications}
              disabled={isCleaningUp}
              className="text-amber-200 border-amber-400/30 bg-amber-400/10 hover:bg-amber-400/20 h-9 admin-focus-mobile"
            >
              <Clock className={`h-4 w-4 mr-2 ${isCleaningUp ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">{isCleaningUp ? 'Cleaning...' : 'Clean Expired'}</span>
              <span className="sm:hidden">{isCleaningUp ? 'Cleaning...' : 'Clean'}</span>
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  variant="destructive"
                  className="bg-red-500/80 hover:bg-red-500/90 text-white h-9 admin-focus-mobile"
                  disabled={notifications.length === 0 || isDeleting}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Delete All</span>
                  <span className="sm:hidden">Delete</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-vista-dark-lighter border-vista-light/10 shadow-md rounded-lg mx-4 max-w-md sm:max-w-lg">
                <AlertDialogHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-vista-dark p-2 rounded-full shadow-sm w-10 h-10 flex items-center justify-center">
                      <Trash2 className="h-5 w-5 text-vista-light" />
                    </div>
                    <AlertDialogTitle className="text-lg sm:text-xl text-vista-light">Are you absolutely sure?</AlertDialogTitle>
                  </div>
                  <AlertDialogDescription className="text-vista-light/70 mt-2 text-sm sm:text-base">
                    This action will permanently delete <span className="font-medium">all notifications</span> from all users.
                    <br />It cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="mt-4 border-t border-vista-light/10 pt-4 flex-col sm:flex-row gap-2">
                  <AlertDialogCancel className="bg-vista-dark border-vista-light/10 text-vista-light hover:bg-vista-dark/80 w-full sm:w-auto">
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAllNotifications}
                    className="bg-red-500/80 hover:bg-red-500/90 text-white w-full sm:w-auto"
                    disabled={isDeleting}
                  >
                    {isDeleting ? 'Deleting...' : 'Delete All'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <NotificationsStats
        notifications={notifications}
        isLoading={loading}
      />

      {/* Mobile-optimized Notification Creation Dialog */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="bg-vista-dark-lighter border-vista-light/10 max-w-[95vw] sm:max-w-3xl shadow-md rounded-lg mx-4 max-h-[90vh] overflow-hidden">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg sm:text-xl text-vista-light flex items-center gap-2 sm:gap-3">
              <div className="bg-vista-blue/20 p-1.5 sm:p-2 rounded-full shadow-sm w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center flex-shrink-0">
                <Plus className="h-4 w-4 sm:h-5 sm:w-5 text-vista-blue" />
              </div>
              <span className="truncate">Create New Notification</span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto admin-scroll-mobile pr-2">
            <NotificationCreationForm
              onSubmit={onSubmit}
              isSubmitting={isSubmitting}
            />
          </div>

          <DialogFooter className="border-t border-vista-light/10 pt-4 mt-2 flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setShowCreateForm(false)}
              className="text-vista-light border-vista-light/10 hover:bg-vista-dark hover:text-vista-light w-full sm:w-auto admin-focus-mobile"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile-optimized Notification Tabs */}
      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md">
        <CardHeader className="admin-card-header-mobile">
          <CardTitle className="admin-title-mobile sm:text-xl text-vista-light flex items-center">
            <div className="bg-vista-dark p-1.5 sm:p-2 rounded-full mr-2 sm:mr-3 shadow-sm w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center flex-shrink-0">
              <Bell className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
            </div>
            <span className="truncate">All Notifications</span>
          </CardTitle>
          <CardDescription className="admin-subtitle-mobile ml-0 sm:ml-1">
            View and manage all notifications sent to users
          </CardDescription>
        </CardHeader>
        <CardContent className="admin-card-content-mobile">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-4 sm:mb-5">
            {/* Mobile-first redesigned tabs */}
            <div className="mb-4 sm:mb-6">
              {/* Mobile: Horizontal scrollable tabs */}
              <div className="block sm:hidden">
                <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
                  <TabsTrigger
                    value="all"
                    className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                      activeTab === 'all'
                        ? 'bg-vista-blue text-white shadow-lg'
                        : 'bg-vista-dark border border-vista-light/20 text-vista-light/70 hover:text-vista-light hover:border-vista-light/40'
                    }`}
                    onClick={() => setActiveTab('all')}
                  >
                    All
                  </TabsTrigger>
                  <TabsTrigger
                    value="system"
                    className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                      activeTab === 'system'
                        ? 'bg-slate-500 text-white shadow-lg'
                        : 'bg-vista-dark border border-vista-light/20 text-vista-light/70 hover:text-vista-light hover:border-vista-light/40'
                    }`}
                    onClick={() => setActiveTab('system')}
                  >
                    System
                  </TabsTrigger>
                  <TabsTrigger
                    value="update"
                    className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                      activeTab === 'update'
                        ? 'bg-amber-500 text-white shadow-lg'
                        : 'bg-vista-dark border border-vista-light/20 text-vista-light/70 hover:text-vista-light hover:border-vista-light/40'
                    }`}
                    onClick={() => setActiveTab('update')}
                  >
                    Updates
                  </TabsTrigger>
                  <TabsTrigger
                    value="new_content"
                    className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                      activeTab === 'new_content'
                        ? 'bg-vista-blue text-white shadow-lg'
                        : 'bg-vista-dark border border-vista-light/20 text-vista-light/70 hover:text-vista-light hover:border-vista-light/40'
                    }`}
                    onClick={() => setActiveTab('new_content')}
                  >
                    Content
                  </TabsTrigger>
                  <TabsTrigger
                    value="recommendation"
                    className={`flex-shrink-0 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 ${
                      activeTab === 'recommendation'
                        ? 'bg-green-500 text-white shadow-lg'
                        : 'bg-vista-dark border border-vista-light/20 text-vista-light/70 hover:text-vista-light hover:border-vista-light/40'
                    }`}
                    onClick={() => setActiveTab('recommendation')}
                  >
                    Recs
                  </TabsTrigger>
                </div>
              </div>

              {/* Desktop: Traditional tabs */}
              <div className="hidden sm:flex justify-center">
                <TabsList className="bg-vista-dark border border-vista-light/10 p-1 rounded-lg shadow-sm">
                  <TabsTrigger
                    value="all"
                    className="data-[state=active]:bg-vista-blue data-[state=active]:text-white text-vista-light/80 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200"
                  >
                    All Notifications
                  </TabsTrigger>
                  <TabsTrigger
                    value="system"
                    className="data-[state=active]:bg-slate-500 data-[state=active]:text-white text-vista-light/80 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200"
                  >
                    System
                  </TabsTrigger>
                  <TabsTrigger
                    value="update"
                    className="data-[state=active]:bg-amber-500 data-[state=active]:text-white text-vista-light/80 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200"
                  >
                    Updates
                  </TabsTrigger>
                  <TabsTrigger
                    value="new_content"
                    className="data-[state=active]:bg-vista-blue data-[state=active]:text-white text-vista-light/80 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200"
                  >
                    New Content
                  </TabsTrigger>
                  <TabsTrigger
                    value="recommendation"
                    className="data-[state=active]:bg-green-500 data-[state=active]:text-white text-vista-light/80 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200"
                  >
                    Recommendations
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {loading ? (
              <div className="admin-spacing-mobile mt-4 sm:mt-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex gap-3 sm:gap-4 p-3 sm:p-5 bg-vista-dark/40 rounded-xl border border-vista-light/5 animate-pulse">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-vista-dark-lighter flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-5 sm:h-6 w-32 sm:w-48 bg-vista-dark-lighter rounded-md mb-2 sm:mb-3"></div>
                      <div className="h-3 sm:h-4 w-20 sm:w-24 bg-vista-dark-lighter rounded-md mb-3 sm:mb-4"></div>
                      <div className="h-3 w-full bg-vista-dark-lighter rounded-md mb-2 sm:mb-3"></div>
                      <div className="flex flex-wrap gap-2 sm:gap-3">
                        <div className="h-4 sm:h-5 w-20 sm:w-24 bg-vista-dark-lighter rounded-full"></div>
                        <div className="h-4 sm:h-5 w-24 sm:w-32 bg-vista-dark-lighter rounded-full"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mt-4 sm:mt-6 bg-red-950/40 border-red-500/40 text-red-300 shadow-md rounded-xl mx-2 sm:mx-0">
                <div className="flex items-start gap-3">
                  <div className="bg-red-500/10 p-1.5 sm:p-2 rounded-full flex-shrink-0">
                    <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-red-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <AlertTitle className="admin-title-mobile sm:text-lg font-medium mb-1">Error Loading Notifications</AlertTitle>
                    <AlertDescription className="admin-text-mobile text-red-300/90">{error}</AlertDescription>
                  </div>
                </div>
              </Alert>
            ) : filteredNotifications.length === 0 ? (
              <div className="text-center py-8 sm:py-12 bg-gradient-to-b from-vista-dark/40 to-vista-dark/30 rounded-xl border border-vista-light/10 mt-4 sm:mt-6 shadow-inner mx-2 sm:mx-0">
                <div className="bg-vista-dark-lighter p-4 sm:p-5 rounded-full w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-5 flex items-center justify-center shadow-lg">
                  <Bell className="h-7 w-7 sm:h-9 sm:w-9 text-vista-light/40" />
                </div>
                <h3 className="admin-title-mobile sm:text-xl font-medium text-vista-light mb-2 sm:mb-3">No notifications found</h3>
                <p className="admin-text-mobile text-vista-light/70 max-w-md mx-auto mb-4 sm:mb-6 px-4">
                  {activeTab === 'all'
                    ? "You haven't sent any notifications yet. Create a new notification to get started."
                    : `You haven't sent any ${activeTab.replace('_', ' ')} notifications yet.`}
                </p>
                {activeTab !== 'all' ? (
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('all')}
                    className="text-vista-light border-vista-light/20 hover:bg-vista-dark transition-all duration-200 shadow-sm hover:shadow w-full sm:w-auto admin-focus-mobile"
                  >
                    View all notifications
                  </Button>
                ) : (
                  <Button
                    onClick={() => setShowCreateForm(true)}
                    className="bg-vista-blue hover:bg-vista-blue/90 transition-all duration-200 shadow-sm hover:shadow w-full sm:w-auto admin-focus-mobile"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Notification
                  </Button>
                )}
              </div>
            ) : (
              <div className="admin-spacing-mobile mt-4 sm:mt-6">
                {filteredNotifications.map((notification) => (
                  <NotificationCard
                    key={notification._id}
                    notification={notification}
                    onDelete={handleDeleteNotification}
                    isDeleting={isDeleting}
                    deletingIds={deletingIds}
                  />
                ))}
              </div>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
