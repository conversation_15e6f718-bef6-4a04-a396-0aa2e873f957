import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON>, Eye, History, BarC<PERSON>, Clock, AlertCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { NotificationType } from '@/models/Notification';
import { AnimatedCounter } from '@/components/admin/AnimatedCounter';
import { formatDistanceToNow } from 'date-fns';

interface NotificationsStatsProps {
  notifications: Array<{
    _id: string;
    type: NotificationType;
    title: string;
    message: string;
    read: boolean;
    createdAt: string;
    expiresAt?: string;
    userId?: string;
    recipientCount?: number;
  }>;
  isLoading: boolean;
}

export function NotificationsStats({ notifications, isLoading }: NotificationsStatsProps) {
  const [stats, setStats] = useState({
    total: 0,
    totalRecipients: 0,
    unread: 0,
    readPercentage: 0,
    byType: {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    },
    typePercentages: {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    },
    mostRecent: null as string | null,
    nextExpiration: null as string | null,
    withExpiration: 0,
    withoutExpiration: 0,
  });

  // Calculate stats when notifications change
  useEffect(() => {
    if (notifications.length === 0) {
      setStats({
        total: 0,
        totalRecipients: 0,
        unread: 0,
        readPercentage: 0,
        byType: {
          system: 0,
          update: 0,
          new_content: 0,
          recommendation: 0,
        },
        typePercentages: {
          system: 0,
          update: 0,
          new_content: 0,
          recommendation: 0,
        },
        mostRecent: null,
        nextExpiration: null,
        withExpiration: 0,
        withoutExpiration: 0,
      });
      return;
    }

    // Calculate total notifications (unique broadcasts)
    const total = notifications.length;

    // Calculate total recipients (sum of all recipientCount values)
    const totalRecipients = notifications.reduce((sum, n) => sum + (n.recipientCount || 1), 0);

    // Calculate unread notifications
    const unread = notifications.filter(n => !n.read).length;
    const readPercentage = Math.round(((total - unread) / total) * 100);

    // Count by type
    const byType = {
      system: 0,
      update: 0,
      new_content: 0,
      recommendation: 0,
    };

    notifications.forEach(n => {
      byType[n.type as keyof typeof byType] += 1;
    });

    // Calculate percentages
    const typePercentages = {
      system: Math.round((byType.system / total) * 100),
      update: Math.round((byType.update / total) * 100),
      new_content: Math.round((byType.new_content / total) * 100),
      recommendation: Math.round((byType.recommendation / total) * 100),
    };

    // Get most recent
    const sorted = [...notifications].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    const mostRecent = sorted.length > 0 ? sorted[0].createdAt : null;

    // Calculate expiration stats
    const withExpiration = notifications.filter(n => n.expiresAt).length;
    const withoutExpiration = total - withExpiration;

    // Find the next notification to expire
    const now = new Date();
    const futureExpirations = notifications
      .filter(n => n.expiresAt && new Date(n.expiresAt) > now)
      .sort((a, b) => new Date(a.expiresAt!).getTime() - new Date(b.expiresAt!).getTime());

    const nextExpiration = futureExpirations.length > 0 ? futureExpirations[0].expiresAt || null : null;

    setStats({
      total,
      totalRecipients,
      unread,
      readPercentage,
      byType,
      typePercentages,
      mostRecent,
      nextExpiration,
      withExpiration,
      withoutExpiration,
    });
  }, [notifications]);

  // Format the most recent date
  const formattedMostRecent = stats.mostRecent
    ? new Date(stats.mostRecent).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'N/A';

  // Format the next expiration date
  const formattedNextExpiration = stats.nextExpiration
    ? new Date(stats.nextExpiration).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'N/A';

  // Get relative time for next expiration
  const expirationTimeFromNow = stats.nextExpiration
    ? formatDistanceToNow(new Date(stats.nextExpiration), { addSuffix: true })
    : 'N/A';

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-vista-dark-lighter/50 border-vista-light/10">
            <CardHeader className="admin-card-header-mobile pb-2">
              <div className="h-4 sm:h-5 w-20 sm:w-24 bg-vista-dark-lighter rounded-md animate-pulse"></div>
            </CardHeader>
            <CardContent className="admin-card-content-mobile">
              <div className="h-6 sm:h-8 w-12 sm:w-16 bg-vista-dark-lighter rounded-md animate-pulse mb-2"></div>
              <div className="h-3 sm:h-4 w-full bg-vista-dark-lighter rounded-md animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-5 mb-4 sm:mb-6">
      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-blue/50"></div>
        <CardHeader className="admin-card-header-mobile pb-2 pt-3 sm:pt-4">
          <CardTitle className="admin-title-mobile sm:text-lg text-vista-light flex items-center">
            <div className="bg-vista-dark w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-md flex-shrink-0">
              <Bell className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
            </div>
            <span className="truncate">Total Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="admin-card-content-mobile">
          <div className="text-2xl sm:text-4xl font-bold text-vista-light flex items-baseline">
            <AnimatedCounter value={stats.total} />
          </div>
          <div className="admin-text-mobile text-vista-light/70 mt-2 space-y-1">
            <p className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2 flex-shrink-0"></span>
              <span className="truncate">Unique notifications</span>
            </p>
            <p className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-vista-blue/40 mr-2 flex-shrink-0"></span>
              <span className="text-vista-light/80 truncate">
                <span className="font-medium">{stats.totalRecipients}</span>
                <span className="hidden sm:inline"> total recipients</span>
                <span className="sm:hidden"> recipients</span>
              </span>
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="admin-card-header-mobile pb-2 pt-3 sm:pt-4">
          <CardTitle className="admin-title-mobile sm:text-lg text-vista-light flex items-center">
            <div className="bg-vista-dark w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-md flex-shrink-0">
              <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
            </div>
            <span className="truncate">Read Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="admin-card-content-mobile">
          <div className="text-2xl sm:text-4xl font-bold text-vista-light flex items-baseline">
            <AnimatedCounter value={stats.total - stats.unread} />
            <span className="text-sm sm:text-base font-normal text-vista-light/60 ml-2"> / {stats.total}</span>
          </div>
          <div className="mt-3 sm:mt-4 space-y-2">
            <div className="flex items-center justify-between admin-text-mobile">
              <span className="text-vista-light/70 flex items-center">
                <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2 flex-shrink-0"></span>
                Read
              </span>
              <span className="text-vista-light/90 font-medium">{stats.readPercentage}%</span>
            </div>
            <Progress
              value={stats.readPercentage}
              className="h-2 bg-vista-dark rounded-full"
              indicatorClassName="bg-vista-light/50"
              style={{
                transition: 'width 1.5s cubic-bezier(0.65, 0, 0.35, 1)'
              }}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="admin-card-header-mobile pb-2 pt-3 sm:pt-4">
          <CardTitle className="admin-title-mobile sm:text-lg text-vista-light flex items-center">
            <div className="bg-vista-dark w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-md flex-shrink-0">
              <BarChart className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
            </div>
            <span className="truncate">By Type</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="admin-card-content-mobile">
          <div className="space-y-2 sm:space-y-3">
            {Object.entries(stats.byType).map(([type, count]) => (
              count > 0 && (
                <div key={type} className="space-y-1.5 sm:space-y-2">
                  <div className="flex justify-between items-center admin-text-mobile">
                    <span className="text-vista-light/70 capitalize flex items-center min-w-0 flex-1">
                      <span className="inline-block w-2 h-2 rounded-full bg-vista-light/40 mr-2 flex-shrink-0"></span>
                      <span className="truncate">{type.replace('_', ' ')}</span>
                    </span>
                    <span className="text-vista-light/90 font-medium ml-2 flex-shrink-0">{count}</span>
                  </div>
                  <Progress
                    value={stats.typePercentages[type as keyof typeof stats.typePercentages]}
                    className="h-2 bg-vista-dark rounded-full"
                    indicatorClassName="bg-vista-light/50"
                    style={{
                      transition: 'width 1.5s cubic-bezier(0.65, 0, 0.35, 1)'
                    }}
                  />
                </div>
              )
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-vista-dark-lighter border-vista-light/10 shadow-md overflow-hidden">
        <div className="h-1 w-full bg-vista-light/30"></div>
        <CardHeader className="admin-card-header-mobile pb-2 pt-3 sm:pt-4">
          <CardTitle className="admin-title-mobile sm:text-lg text-vista-light flex items-center">
            <div className="bg-vista-dark w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center mr-2 sm:mr-3 shadow-md flex-shrink-0">
              <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-vista-light" />
            </div>
            <span className="truncate">Timing</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="admin-card-content-mobile">
          <div className="space-y-3 sm:space-y-4">
            <div>
              <div className="admin-text-mobile font-medium text-vista-light/70 mb-1 flex items-center">
                <History className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
                <span className="truncate">Most Recent</span>
              </div>
              <div className="text-base sm:text-lg font-semibold text-vista-light truncate">{formattedMostRecent.split(',')[0]}</div>
              <div className="admin-text-mobile font-medium text-vista-light/60 truncate">{formattedMostRecent.split(',')[1]}</div>
            </div>

            <div className="border-t border-vista-light/10 pt-2 sm:pt-3">
              <div className="admin-text-mobile font-medium text-vista-light/70 mb-1 flex items-center">
                <AlertCircle className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
                <span className="truncate">Next Expiration</span>
              </div>
              {stats.nextExpiration ? (
                <>
                  <div className="text-base sm:text-lg font-semibold text-amber-300 truncate">{formattedNextExpiration.split(',')[0]}</div>
                  <div className="admin-text-mobile font-medium text-amber-300/60 truncate">{formattedNextExpiration.split(',')[1]}</div>
                  <div className="admin-caption-mobile text-amber-300/80 mt-1 truncate">
                    Expires {expirationTimeFromNow}
                  </div>
                </>
              ) : (
                <div className="admin-text-mobile text-vista-light/60">No scheduled expirations</div>
              )}
            </div>

            <div className="admin-caption-mobile text-vista-light/70 flex flex-col sm:flex-row sm:items-center sm:justify-between border-t border-vista-light/10 pt-2 gap-1 sm:gap-0">
              <span className="truncate">{stats.withExpiration} with expiration</span>
              <span className="truncate">{stats.withoutExpiration} without expiration</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

