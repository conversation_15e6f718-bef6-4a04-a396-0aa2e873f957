import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import mongoose from 'mongoose';

// Type definitions for MongoDB documents
interface ContentDocument {
  _id: mongoose.Types.ObjectId;
  title: string;
  type: string;
  posterPath?: string;
  backdropPath?: string;
  year?: number;
  genres?: string[];
  featured?: boolean;
}

interface FeaturedContentDocument {
  _id: mongoose.Types.ObjectId;
  contentId: ContentDocument;
  order: number;
  startDate?: Date;
  endDate?: Date;
}

/**
 * GET /api/admin/featured
 * Get all featured content
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB first
    await ensureMongooseConnection();

    // Check if user is admin
    const User = mongoose.models.User || mongoose.model('User', new mongoose.Schema({ role: String }));
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Define the Content schema directly
    const ContentSchema = new mongoose.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.models.Content ||
                   mongoose.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.Schema({
      contentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.models.FeaturedContent ||
                           mongoose.model('FeaturedContent', FeaturedContentSchema);

    // Fetch featured content with content details
    const featuredContent = await FeaturedContent.find()
      .sort({ order: 1 })
      .populate('contentId', 'title type posterPath backdropPath year genres');

    // Transform data for response
    const transformedContent = await Promise.all(
      featuredContent.map(async (item: FeaturedContentDocument) => {
        const content = item.contentId as ContentDocument;
        return {
          id: item._id.toString(),
          contentId: content._id.toString(),
          title: content.title,
          type: content.type,
          posterPath: content.posterPath,
          backdropPath: content.backdropPath,
          year: content.year,
          genres: content.genres,
          order: item.order,
          startDate: item.startDate,
          endDate: item.endDate
        };
      })
    );

    // Return featured content
    return NextResponse.json(transformedContent);
  } catch (error) {
    console.error('Error fetching featured content:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/featured
 * Add content to featured
 */
export async function POST(request: NextRequest) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Check if user is admin
    const User = mongoose.models.User || mongoose.model('User', new mongoose.Schema({ role: String }));
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Content schema directly
    const ContentSchema = new mongoose.Schema({
      title: String,
      type: String,
      posterPath: String,
      backdropPath: String,
      year: Number,
      genres: [String],
      featured: Boolean
    }, {
      timestamps: true
    });

    // Get the Content model
    const Content = mongoose.models.Content ||
                   mongoose.model('Content', ContentSchema);

    // Define the FeaturedContent schema directly
    const FeaturedContentSchema = new mongoose.Schema({
      contentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Content'
      },
      order: Number,
      startDate: Date,
      endDate: Date
    }, {
      timestamps: true
    });

    // Get the FeaturedContent model
    const FeaturedContent = mongoose.models.FeaturedContent ||
                           mongoose.model('FeaturedContent', FeaturedContentSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.Schema({
      userId: mongoose.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.models.UserActivity ||
                        mongoose.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.contentId) {
      return NextResponse.json({ error: 'Content ID is required' }, { status: 400 });
    }

    // Check if content exists
    const content = await Content.findById(data.contentId);
    if (!content) {
      return NextResponse.json({ error: 'Content not found' }, { status: 404 });
    }

    // Check if content is already featured
    const existingFeatured = await FeaturedContent.findOne({ contentId: data.contentId });
    if (existingFeatured) {
      return NextResponse.json({ error: 'Content is already featured' }, { status: 400 });
    }

    // Get highest order
    const highestOrder = await FeaturedContent.findOne().sort({ order: -1 });
    const newOrder = highestOrder ? highestOrder.order + 1 : 1;

    // Create new featured content
    const featuredContent = new FeaturedContent({
      contentId: data.contentId,
      order: data.order || newOrder,
      startDate: data.startDate,
      endDate: data.endDate
    });

    // Save featured content
    await featuredContent.save();

    // Update content to mark as featured
    await Content.findByIdAndUpdate(data.contentId, { featured: true });

    // Log admin activity directly
    if (userId) {
      await UserActivity.create({
        userId: new mongoose.Types.ObjectId(userId),
        type: 'admin',
        action: 'add_featured_content',
        details: `Admin added content to featured: ${(content as ContentDocument).title}`,
        ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date(),
        metadata: { contentId: data.contentId }
      });
    }

    // Return new featured content with content details
    const populatedFeatured = await FeaturedContent.findById(featuredContent._id)
      .populate('contentId', 'title type posterPath backdropPath year genres');

    const populatedContent = populatedFeatured.contentId as ContentDocument;
    return NextResponse.json({
      id: populatedFeatured._id.toString(),
      contentId: populatedContent._id.toString(),
      title: populatedContent.title,
      type: populatedContent.type,
      posterPath: populatedContent.posterPath,
      backdropPath: populatedContent.backdropPath,
      year: populatedContent.year,
      genres: populatedContent.genres,
      order: populatedFeatured.order,
      startDate: populatedFeatured.startDate,
      endDate: populatedFeatured.endDate
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding featured content:', error);
    return NextResponse.json(
      { error: 'Failed to add featured content', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
