---
alwaysApply: false
---
# React Patterns for StreamVista

## Component Architecture

### Server vs. Client Components
- **Server Components First**: Always start with a server component unless client-side interactivity is required
- **Clear Demarcation**: Explicitly mark client components with `'use client'` directive at the top of the file
- **Minimal Client Surface Area**: Minimize the size and scope of client components
- **Interactivity Isolation**: Isolate interactive parts into specific client components

```typescript
// ✅ CORRECT - Server Component (default)
function UserProfile({ userId }: { userId: string }): JSX.Element {
  // Data fetching, etc.
  return (
    <div>
      <UserInfo user={user} />
      <UserActivityClientWrapper user={user} />
    </div>
  );
}

// ✅ CORRECT - Client Component
'use client';

import { useState } from 'react';

function UserActivityClientWrapper({ user }: { user: User }): JSX.Element {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  
  return (
    <div>
      <button onClick={() => setIsExpanded(!isExpanded)}>
        {isExpanded ? 'Show Less' : 'Show More'}
      </button>
      {isExpanded && <UserActivity userId={user.id} />}
    </div>
  );
}
```

### Component Structure
- **Function Declaration**: Always use `function` keyword for components, not arrow functions or const assignments
- **Props Destructuring**: Destructure props in the parameter list
- **Props Typing**: Always use TypeScript interfaces for props
- **Return Type**: Explicitly type the return value as JSX.Element
- **Component Organization**: Follow this order in component files:
  1. Component declaration
  2. Child components
  3. Helper functions
  4. Constants and static content

```typescript
// ✅ CORRECT
interface UserCardProps {
  user: User;
  isActive: boolean;
  onSelect: (userId: string) => void;
}

function UserCard({ user, isActive, onSelect }: UserCardProps): JSX.Element {
  const handleSelect = (): void => {
    onSelect(user.id);
  };
  
  return (
    <div className={`user-card ${isActive ? 'active' : ''}`}>
      <h3>{user.name}</h3>
      <button onClick={handleSelect}>Select</button>
    </div>
  );
}

// Helper functions
function formatUserName(name: string): string {
  // Format logic
  return name;
}

// Constants
const USER_CARD_VARIANTS = {
  primary: 'bg-blue-500',
  secondary: 'bg-gray-500'
} as const;
```

## Hooks and State Management

### Custom Hooks
- **Naming Convention**: Prefix all custom hooks with `use`
- **Return Type**: Explicitly type the return value
- **Typescript Generics**: Use generics for flexible, reusable hooks
- **Error Handling**: Include proper error handling in hooks
- **Documentation**: Document parameters and return values

```typescript
// ✅ CORRECT
interface UseUserResult {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

function useUser(userId: string): UseUserResult {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Implementation...
  
  return { user, isLoading, error, refetch };
}
```

### State Management Patterns
- **Local State**: Use `useState` for component-specific state
- **Shared State**: Use React Context for shared state across components
- **Server State**: Use `useQuery` or SWR for server state management
- **Form State**: Use React Hook Form with Zod validation
- **State Initialization**: Always provide proper type and initial value

```typescript
// ✅ CORRECT - Local State
function Counter(): JSX.Element {
  const [count, setCount] = useState<number>(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}

// ✅ CORRECT - Context State
interface ThemeContextValue {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

function ThemeProvider({ children }: { children: React.ReactNode }): JSX.Element {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  
  const toggleTheme = useCallback((): void => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  }, []);
  
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
```

## UI Component Patterns

### Composition Over Configuration
- **Prefer Composition**: Use component composition rather than complex props
- **Component API Design**: Keep component APIs simple and focused
- **Children Prop**: Use the children prop for flexible content placement
- **Slot Pattern**: Use slots for complex component structures

```typescript
// ✅ CORRECT
function Card({ children, className }: { children: React.ReactNode; className?: string }): JSX.Element {
  return <div className={`card ${className || ''}`}>{children}</div>;
}

function CardHeader({ children }: { children: React.ReactNode }): JSX.Element {
  return <div className="card-header">{children}</div>;
}

function CardContent({ children }: { children: React.ReactNode }): JSX.Element {
  return <div className="card-content">{children}</div>;
}

function CardFooter({ children }: { children: React.ReactNode }): JSX.Element {
  return <div className="card-footer">{children}</div>;
}

// Usage
function UserProfileCard({ user }: { user: User }): JSX.Element {
  return (
    <Card>
      <CardHeader>
        <h2>{user.name}</h2>
      </CardHeader>
      <CardContent>
        <p>{user.bio}</p>
      </CardContent>
      <CardFooter>
        <button>View Profile</button>
      </CardFooter>
    </Card>
  );
}
```

### Conditional Rendering
- **Ternary Operators**: Use ternary operators for simple conditions
- **Logical AND**: Use `&&` for conditional rendering
- **Early Returns**: Use early returns for complex conditions
- **Extracted Components**: Extract complex conditions into separate components
- **Null Coalescence**: Use nullish coalescing for fallbacks

```typescript
// ✅ CORRECT
function UserStatus({ user }: { user: User | null }): JSX.Element {
  // Early return for null case
  if (!user) {
    return <div>No user data available</div>;
  }
  
  // Ternary for simple condition
  return (
    <div>
      <span className={user.isActive ? 'status-active' : 'status-inactive'}>
        {user.isActive ? 'Active' : 'Inactive'}
      </span>
      
      {/* Logical AND for conditional render */}
      {user.lastSeen && <span>Last seen: {formatDate(user.lastSeen)}</span>}
      
      {/* Nullish coalescing for fallbacks */}
      <span>Role: {user.role ?? 'User'}</span>
    </div>
  );
}
```

## Performance Optimizations

### Code Splitting
- **Dynamic Imports**: Use Next.js dynamic imports for code splitting
- **Lazy Loading**: Lazy load components that aren't needed for initial render
- **Route-Based Splitting**: Split code based on routes
- **Component Boundaries**: Create logical component boundaries for code splitting

```typescript
// ✅ CORRECT
import dynamic from 'next/dynamic';

// Dynamic import with loading state
const DashboardAnalytics = dynamic(
  () => import('@/components/dashboard/analytics'),
  {
    loading: () => <LoadingSpinner />,
    ssr: false // Disable SSR for components with browser-only APIs
  }
);

function Dashboard(): JSX.Element {
  const [showAnalytics, setShowAnalytics] = useState<boolean>(false);
  
  return (
    <div>
      <button onClick={() => setShowAnalytics(!showAnalytics)}>
        {showAnalytics ? 'Hide' : 'Show'} Analytics
      </button>
      
      {showAnalytics && <DashboardAnalytics />}
    </div>
  );
}
```

### Memoization
- **React.memo**: Use for pure components that render often with the same props
- **useMemo**: Use for expensive calculations
- **useCallback**: Use for callbacks passed to optimized child components
- **Dependencies Array**: Always specify complete dependencies array

```typescript
// ✅ CORRECT
function ExpensiveList({ items }: { items: Item[] }): JSX.Element {
  // Memoize sorted items
  const sortedItems = useMemo(() => {
    return [...items].sort((a, b) => a.name.localeCompare(b.name));
  }, [items]);
  
  // Memoize callback
  const handleItemClick = useCallback((id: string): void => {
    console.log(`Item clicked: ${id}`);
  }, []);
  
  return (
    <ul>
      {sortedItems.map(item => (
        <ListItem 
          key={item.id} 
          item={item} 
          onClick={handleItemClick} 
        />
      ))}
    </ul>
  );
}

// Memoize pure component
const ListItem = React.memo(function ListItem({ 
  item, 
  onClick 
}: { 
  item: Item; 
  onClick: (id: string) => void;
}): JSX.Element {
  return (
    <li onClick={() => onClick(item.id)}>
      {item.name}
    </li>
  );
});
```

## Error Handling

### Error Boundaries
- **Component-Level Boundaries**: Wrap components with error boundaries
- **Route-Level Boundaries**: Use Next.js error.tsx for route-level error handling
- **Fallback UI**: Provide user-friendly fallback UI
- **Error Reporting**: Include error reporting in error boundaries

```typescript
'use client';

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    // Log error to reporting service
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

// Error page for Next.js App Router
// src/app/error.tsx
'use client';

function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}): JSX.Element {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={reset}>Try again</button>
    </div>
  );
}
```

### Try-Catch Patterns
- **Async Operations**: Always use try-catch for async operations
- **Error Typing**: Type errors properly
- **User Feedback**: Provide user-friendly error messages
- **Error State**: Update component state with error information

```typescript
// ✅ CORRECT
function ProfilePage({ userId }: { userId: string }): JSX.Element {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  useEffect(() => {
    async function fetchUser(): Promise<void> {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch user: ${response.statusText}`);
        }
        
        const userData: User = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    }
    
    void fetchUser();
  }, [userId]);
  
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  if (error) {
    return <ErrorDisplay message={error.message} />;
  }
  
  if (!user) {
    return <div>No user found</div>;
  }
  
  return <UserProfile user={user} />;
}
```

## Forms and Validation

### Form Structure
- **React Hook Form**: Use for form state management
- **Zod Validation**: Use for schema validation
- **Controlled Components**: Use for complex form interactions
- **Form Submission**: Handle form submission with proper error handling

```typescript
// ✅ CORRECT
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const userSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  age: z.number().min(18, 'Must be at least 18 years old'),
});

type UserFormData = z.infer<typeof userSchema>;

function UserForm(): JSX.Element {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: '',
      email: '',
      age: undefined,
    },
  });
  
  const onSubmit = async (data: UserFormData): Promise<void> => {
    try {
      await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div>
        <label htmlFor="name">Name</label>
        <input id="name" {...register('name')} />
        {errors.name && <p>{errors.name.message}</p>}
      </div>
      
      <div>
        <label htmlFor="email">Email</label>
        <input id="email" type="email" {...register('email')} />
        {errors.email && <p>{errors.email.message}</p>}
      </div>
      
      <div>
        <label htmlFor="age">Age</label>
        <input 
          id="age" 
          type="number" 
          {...register('age', { valueAsNumber: true })} 
        />
        {errors.age && <p>{errors.age.message}</p>}
      </div>
      
      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
}
```

## Accessibility

### ARIA Attributes
- **Semantic HTML**: Use semantic HTML elements
- **ARIA Roles**: Add ARIA roles for complex UI
- **Focus Management**: Manage focus properly
- **Keyboard Navigation**: Ensure keyboard accessibility
- **Screen Reader Support**: Provide screen reader support

```typescript
// ✅ CORRECT
function Accordion({ items }: { items: AccordionItem[] }): JSX.Element {
  const [openItem, setOpenItem] = useState<string | null>(null);
  
  return (
    <div className="accordion" role="tablist">
      {items.map(item => {
        const isOpen = openItem === item.id;
        const headerId = `accordion-header-${item.id}`;
        const panelId = `accordion-panel-${item.id}`;
        
        return (
          <div key={item.id} className="accordion-item">
            <h3>
              <button
                id={headerId}
                className="accordion-header"
                onClick={() => setOpenItem(isOpen ? null : item.id)}
                aria-expanded={isOpen}
                aria-controls={panelId}
                role="tab"
              >
                {item.title}
                <span aria-hidden="true">{isOpen ? '▲' : '▼'}</span>
              </button>
            </h3>
            
            <div
              id={panelId}
              role="tabpanel"
              aria-labelledby={headerId}
              className="accordion-panel"
              hidden={!isOpen}
            >
              {item.content}
            </div>
          </div>
        );
      })}
    </div>
  );
}
```

## Progressive Enhancement

### Graceful Degradation
- **Core Functionality**: Ensure core functionality works without JavaScript
- **Feature Detection**: Use feature detection instead of browser detection
- **Fallbacks**: Provide fallbacks for unsupported features
- **NoScript Support**: Consider NoScript users

```typescript
// ✅ CORRECT
function ImageGallery({ images }: { images: Image[] }): JSX.Element {
  // Client-side enhancement for image zooming
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  
  // This will only run in client components with JavaScript enabled
  const handleImageClick = (id: string): void => {
    setSelectedImage(id);
  };
  
  return (
    <div>
      {/* Basic functionality works without JavaScript */}
      <div className="image-grid">
        {images.map(image => (
          <a 
            key={image.id}
            href={`/images/${image.id}`}
            onClick={(e) => {
              e.preventDefault();
              handleImageClick(image.id);
            }}
          >
            <img src={image.thumbnail} alt={image.alt} loading="lazy" />
          </a>
        ))}
      </div>
      
      {/* Enhanced functionality with JavaScript */}
      {selectedImage && (
        <dialog open className="image-modal">
          <button 
            onClick={() => setSelectedImage(null)}
            aria-label="Close"
          >
            ×
          </button>
          <img 
            src={images.find(img => img.id === selectedImage)?.fullSize} 
            alt={images.find(img => img.id === selectedImage)?.alt} 
          />
        </dialog>
      )}
    </div>
  );
}
```

## Testing

### Component Testing
- **Unit Tests**: Test component rendering and functionality
- **Integration Tests**: Test component interactions
- **Prop Testing**: Test different prop combinations
- **State Testing**: Test state changes
- **Event Testing**: Test event handlers

```typescript
// ✅ CORRECT
import { render, screen, fireEvent } from '@testing-library/react';
import Counter from './Counter';

describe('Counter', () => {
  test('renders initial count of 0', () => {
    render(<Counter />);
    expect(screen.getByText('Count: 0')).toBeInTheDocument();
  });
  
  test('increments count when button is clicked', () => {
    render(<Counter />);
    fireEvent.click(screen.getByText('Increment'));
    expect(screen.getByText('Count: 1')).toBeInTheDocument();
  });
  
  test('supports custom initial count', () => {
    render(<Counter initialCount={5} />);
    expect(screen.getByText('Count: 5')).toBeInTheDocument();
  });
});
```

## Internationalization

### Translation Integration
- **Next.js i18n**: Use Next.js built-in i18n
- **Translation Keys**: Use descriptive translation keys
- **Interpolation**: Use interpolation for dynamic content
- **Pluralization**: Handle pluralization correctly
- **Right-to-Left Support**: Consider RTL languages

```typescript
// ✅ CORRECT
import { useTranslation } from 'next-i18next';

function Greeting({ name, count }: { name: string; count: number }): JSX.Element {
  const { t } = useTranslation('common');
  
  return (
    <div>
      <h1>{t('greeting.welcome', { name })}</h1>
      <p>
        {t('greeting.notification_count', {
          count,
          defaultValue_plural: 'You have {{count}} notifications',
          defaultValue: 'You have {{count}} notification',
        })}
      </p>
    </div>
  );
}
```

## Mobile Optimization

### Responsive Design
- **Mobile-First Approach**: Design for mobile first, then enhance for larger screens
- **Responsive Images**: Use responsive images with correct sizing
- **Touch Targets**: Ensure touch targets are large enough (at least 44px)
- **Viewport Settings**: Use proper viewport settings
- **Media Queries**: Use media queries for adaptive layouts

```typescript
// ✅ CORRECT
import Image from 'next/image';

function ProductCard({ product }: { product: Product }): JSX.Element {
  return (
    <div className="p-4 md:p-6 rounded-lg shadow-md">
      <div className="relative w-full aspect-square">
        <Image
          src={product.image}
          alt={product.name}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          className="object-cover rounded-md"
          priority={product.featured}
        />
      </div>
      
      <h2 className="text-lg md:text-xl font-bold mt-4">{product.name}</h2>
      
      <p className="text-sm md:text-base mt-2">{product.description}</p>
      
      <div className="flex flex-col sm:flex-row gap-2 mt-4">
        <button className="w-full sm:w-auto py-3 px-4 text-white bg-blue-600 rounded-md">
          Add to Cart
        </button>
        <button className="w-full sm:w-auto py-3 px-4 border border-gray-300 rounded-md">
          Save
        </button>
      </div>
    </div>
  );
}
```

## Code Quality

### Linting and Formatting
- **ESLint**: Follow ESLint rules
- **Prettier**: Use Prettier for code formatting
- **TypeScript Strict Mode**: Enable strict mode
- **Component Best Practices**: Follow component best practices
- **Code Reviews**: Conduct thorough code reviews

```typescript
// ✅ CORRECT
// .eslintrc.js
module.exports = {
  extends: [
    'next/core-web-vitals',
    'prettier',
    'plugin:@typescript-eslint/recommended',
  ],
  rules: {
    'react/display-name': 'off',
    'react/no-unescaped-entities': 'off',
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/explicit-module-boundary-types': 'error',
  },
};
```

