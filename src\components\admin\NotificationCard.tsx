import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { NotificationType } from "@/models/Notification";
import { useAuth } from "@/contexts/AuthContext";
import { format, formatDistanceToNow } from "date-fns";
import {
  AlertCircle,
  Bell,
  Film,
  Info,
  Trash2,
  User,
  Calendar,
  BadgeInfo,
  Clock
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface NotificationCardProps {
  notification: {
    _id: string;
    type: NotificationType;
    title: string;
    message: string;
    contentId?: string;
    contentType?: 'movie' | 'show';
    image?: string;
    read: boolean;
    createdAt: string;
    expiresAt?: string;
    userId?: string;
    recipientCount?: number;
  };
  onDelete: (id: string) => void;
  isDeleting: boolean;
  deletingIds?: string[];
}

export function NotificationCard({ notification, onDelete, isDeleting, deletingIds = [] }: NotificationCardProps) {
  const { _id, type, title, message, createdAt, expiresAt, recipientCount } = notification;
  const { user } = useAuth();

  // Debug expiration date
  console.log('NotificationCard expiresAt:', {
    notificationId: _id,
    expiresAt,
    hasExpiresAt: !!expiresAt,
    type: expiresAt ? typeof expiresAt : 'undefined',
    parsed: expiresAt ? new Date(expiresAt).toISOString() : 'N/A',
    valid: expiresAt ? !isNaN(new Date(expiresAt).getTime()) : false
  });

  // Create a more readable timestamp format
  const formattedDate = format(new Date(createdAt), "MMM d, yyyy 'at' h:mm a");
  const timeAgo = formatDistanceToNow(new Date(createdAt), { addSuffix: true });

  return (
    <Card className="bg-vista-dark-lighter border-vista-light/10 overflow-hidden group shadow-md">
      {/* Subtle colored bar on top based on notification type */}
      <div className={`h-1 w-full ${getNotificationColorClass(type)}`}></div>

      <div className="p-3 sm:p-5 flex flex-col gap-3 sm:gap-4">
        {/* Mobile-optimized header with icon and delete button */}
        <div className="flex items-start gap-3 sm:gap-4">
          {/* Notification icon with color-specific styling */}
          <div className={`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full ${getIconBgClass(type)} flex items-center justify-center shadow-md`}>
            {getNotificationIcon(type)}
          </div>

          {/* Content with clean layout */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2 sm:mb-3">
              <div className="flex-1 min-w-0">
                <h3 className="text-base sm:text-lg font-semibold text-vista-light truncate">
                  {title}
                </h3>
              </div>

              {/* Mobile-optimized delete button */}
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 sm:h-8 sm:w-8 p-0 rounded-full text-vista-light/40 hover:text-red-400 hover:bg-vista-dark flex-shrink-0 admin-focus-mobile"
                onClick={() => {
                  if (user?.id) {
                    onDelete(_id);
                  } else {
                    console.error('Cannot delete notification: User ID not available');
                  }
                }}
                disabled={isDeleting || deletingIds.includes(_id) || !user?.id}
                title={!user?.id ? 'User ID not available' : 'Delete notification'}
              >
                {isDeleting || deletingIds.includes(_id) ? (
                  <div className="h-3.5 w-3.5 sm:h-4 sm:w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent" />
                ) : (
                  <Trash2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                )}
                <span className="sr-only">Delete</span>
              </Button>
            </div>

            {/* Mobile-optimized badges */}
            <div className="flex items-center flex-wrap gap-1.5 sm:gap-2">
              <Badge className={`text-xs font-medium ${getNotificationBadgeClass(type)} px-2 py-0.5`}>
                {formatNotificationType(type)}
              </Badge>
              {recipientCount && (
                <Badge variant="outline" className="text-xs font-medium text-vista-light/70 border-vista-light/10 px-2 py-0.5">
                  <User className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">{recipientCount} recipients</span>
                  <span className="sm:hidden">{recipientCount}</span>
                </Badge>
              )}
              {expiresAt && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="text-xs font-medium bg-amber-400/10 text-amber-200 border-amber-400/30 px-2 py-0.5 shadow-sm">
                        <Clock className="h-3 w-3 mr-1" />
                        <span className="hidden sm:inline">Expires {formatDistanceToNow(new Date(expiresAt), { addSuffix: true })}</span>
                        <span className="sm:hidden">Expires</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent className="bg-vista-dark-lighter border-vista-light/10 text-vista-light">
                      <p>Will be automatically deleted on {new Date(expiresAt).toLocaleDateString()} at {new Date(expiresAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </div>

        {/* Mobile-optimized message */}
        <p className="text-vista-light/80 text-sm sm:text-sm mb-3 sm:mb-4 line-clamp-3 leading-relaxed px-0">
          {message}
        </p>

        {/* Mobile-optimized footer */}
        <div className="flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-2 sm:gap-x-3 sm:gap-y-2 text-xs text-vista-light/70 border-t border-vista-light/10 pt-2 sm:pt-3">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
            <span title={formattedDate} className="truncate">{timeAgo}</span>
          </div>

          {expiresAt ? (
            <div className="flex items-center text-amber-200/90 bg-amber-400/10 px-2 py-1 rounded-md border border-amber-400/20 text-xs">
              <Clock className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
              <span className="hidden sm:inline">Expires on {new Date(expiresAt).toLocaleDateString()} at {new Date(expiresAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
              <span className="sm:hidden">Expires {new Date(expiresAt).toLocaleDateString()}</span>
            </div>
          ) : (
            <div className="flex items-center text-vista-light/70">
              <AlertCircle className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
              <span className="hidden sm:inline">No expiration date set</span>
              <span className="sm:hidden">No expiration</span>
            </div>
          )}

          <div className="flex items-center">
            <BadgeInfo className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1.5 flex-shrink-0" />
            <span className="truncate">ID: {_id.substring(0, 8)}...</span>
          </div>
        </div>
      </div>
    </Card>
  );
}

// Helper function to determine notification icon
function getNotificationIcon(type: NotificationType) {
  switch (type) {
    case 'new_content':
      return <Film className="h-6 w-6 text-vista-blue/90" />;
    case 'recommendation':
      return <Film className="h-6 w-6 text-green-400" />;
    case 'update':
      return <Info className="h-6 w-6 text-amber-300" />;
    case 'system':
      return <Bell className="h-6 w-6 text-slate-300" />;
    default:
      return <Bell className="h-6 w-6 text-vista-light" />;
  }
}

// Helper function to get notification color class - keeping subtle color hints
function getNotificationColorClass(type: NotificationType) {
  switch (type) {
    case 'new_content':
      return 'bg-vista-blue/70';
    case 'recommendation':
      return 'bg-green-500/70';
    case 'update':
      return 'bg-amber-400/70';
    case 'system':
      return 'bg-slate-400/70';
    default:
      return 'bg-vista-light/50';
  }
}

// Helper function to get icon background class
function getIconBgClass(type: NotificationType) {
  switch (type) {
    case 'new_content':
      return 'bg-vista-blue/10';
    case 'recommendation':
      return 'bg-green-500/10';
    case 'update':
      return 'bg-amber-400/10';
    case 'system':
      return 'bg-slate-400/10';
    default:
      return 'bg-vista-dark';
  }
}

// Helper function to get notification badge class - adding subtle color for better readability
function getNotificationBadgeClass(type: NotificationType) {
  switch (type) {
    case 'new_content':
      return 'bg-vista-blue/20 text-vista-blue/90 border-vista-blue/30 shadow-sm';
    case 'recommendation':
      return 'bg-green-500/20 text-green-400 border-green-500/30 shadow-sm';
    case 'update':
      return 'bg-amber-400/20 text-amber-300 border-amber-400/30 shadow-sm';
    case 'system':
      return 'bg-slate-400/20 text-slate-300 border-slate-400/30 shadow-sm';
    default:
      return 'bg-vista-dark text-vista-light border-vista-light/10 shadow-sm';
  }
}

// Helper function to format notification type for display
function formatNotificationType(type: NotificationType) {
  switch (type) {
    case 'new_content':
      return 'New Content';
    case 'recommendation':
      return 'Recommendation';
    case 'update':
      return 'Update';
    case 'system':
      return 'System';
    default:
      return type;
  }
}