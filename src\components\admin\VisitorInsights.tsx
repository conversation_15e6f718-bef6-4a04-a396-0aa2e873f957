'use client';

import { useMemo } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Bar<PERSON>hart
} from '@/components/ui/charts';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  TabsTrigger 
} from '@/components/ui/tabs';
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Clock,
  MousePointerClick,
  ArrowRight,
  AlertCircle
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

interface InsightData {
  // Time series data
  hourlyActivity: {
    hour: number;
    visits: number;
  }[];
  weekdayActivity: {
    day: string;
    visits: number;
  }[];
  
  // Retention data
  retention: {
    cohort: string;
    rates: number[];
  }[];
  
  // Bounce rate and session metrics
  bounceRate: number;
  averageSessionTime: number;
  
  // Page engagement metrics
  topLandingPages: {
    page: string;
    visits: number;
    bounceRate: number;
  }[];
  
  // Conversion funnel
  conversionSteps: {
    step: string;
    visitors: number;
    conversionRate: number;
  }[];
  
  // Anomaly detection
  anomalies: {
    date: string;
    metric: string;
    value: number;
    expected: number;
    percentChange: number;
  }[];
}

interface VisitorInsightsProps {
  data: InsightData;
  isLoading: boolean;
  className?: string;
}

export default function VisitorInsights({ 
  data, 
  isLoading, 
  className = '' 
}: VisitorInsightsProps) {
  // Enhanced safety function to clean NaN values from data
  const cleanNaNValues = (obj: unknown): unknown => {
    if (obj === null || obj === undefined) return obj;

    if (typeof obj === 'number') {
      if (isNaN(obj) || !isFinite(obj)) {
        console.warn('Found NaN/invalid number in data, replacing with 0:', obj);
        return 0;
      }
      return obj;
    }

    if (typeof obj === 'string') {
      // Ensure strings are valid
      return obj || '';
    }

    if (Array.isArray(obj)) {
      return obj.map(cleanNaNValues).filter(item => item !== null && item !== undefined);
    }

    if (typeof obj === 'object') {
      const cleaned: Record<string, unknown> = {};
      for (const key in obj as Record<string, unknown>) {
        const value = (obj as Record<string, unknown>)[key];
        const cleanedValue = cleanNaNValues(value);
        // Only include valid values
        if (cleanedValue !== null && cleanedValue !== undefined) {
          cleaned[key] = cleanedValue;
        }
      }
      return cleaned;
    }

    return obj;
  };

  // Enhanced validation for critical chart data
  const validateChartData = (chartData: any[]): any[] => {
    if (!Array.isArray(chartData)) return [];

    return chartData.map(item => {
      const validatedItem: any = {};

      Object.keys(item).forEach(key => {
        const value = item[key];

        if (typeof value === 'number') {
          // More strict validation for numbers
          const numValue = Number(value);
          validatedItem[key] = Number.isFinite(numValue) && !isNaN(numValue) && numValue >= 0 ? numValue : 0;
        } else if (typeof value === 'string') {
          validatedItem[key] = value || 'Unknown';
        } else {
          validatedItem[key] = value;
        }
      });

      return validatedItem;
    }).filter(item => item && typeof item === 'object');
  };

  // Clean the data to ensure no NaN values
  const cleanData = cleanNaNValues(data) as InsightData;

  // Process hourly activity data to fill in missing hours with enhanced validation
  const processedHourlyData = useMemo(() => {
    if (!cleanData?.hourlyActivity?.length) {
      return Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        hour_label: `${i.toString().padStart(2, '0')}:00`,
        visits: 0
      }));
    }

    const hours = Array.from({ length: 24 }, (_, i) => i);
    const hourMap = new Map();

    // Safely process hourly activity data
    cleanData.hourlyActivity.forEach((item: any) => {
      if (item && typeof item.hour === 'number' && Number.isInteger(item.hour) && item.hour >= 0 && item.hour <= 23) {
        const visits = typeof item.visits === 'number' && Number.isFinite(item.visits) ? Math.max(0, item.visits) : 0;
        hourMap.set(item.hour, visits);
      }
    });

    const result = hours.map(hour => ({
      hour: hour,
      hour_label: `${hour.toString().padStart(2, '0')}:00`,
      visits: hourMap.get(hour) || 0
    }));

    // Use validation function to ensure data integrity
    return validateChartData(result);
  }, [cleanData?.hourlyActivity]);
  
  // Format day names properly
  const processedWeekdayData = useMemo(() => {
    const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    if (!cleanData?.weekdayActivity?.length) {
      return dayOrder.map(day => ({
        day,
        day_short: day.substring(0, 3),
        visits: 0
      }));
    }

    const dayMap = new Map();

    // Safely process weekday activity data
    cleanData.weekdayActivity.forEach((item: any) => {
      if (item && typeof item.day === 'string' && dayOrder.includes(item.day)) {
        const visits = typeof item.visits === 'number' && Number.isFinite(item.visits) ? Math.max(0, item.visits) : 0;
        dayMap.set(item.day, visits);
      }
    });

    const result = dayOrder.map(day => ({
      day,
      day_short: day.substring(0, 3),
      visits: dayMap.get(day) || 0
    }));

    // Use validation function to ensure data integrity
    return validateChartData(result);
  }, [cleanData?.weekdayActivity]);
  
  // Process conversion funnel data for visualization
  const funnelData = useMemo(() => {
    if (!cleanData?.conversionSteps?.length) {
      return [];
    }
    
    return cleanData.conversionSteps.map((step, index) => {
      // Ensure step has valid data
      const visitors = typeof step.visitors === 'number' && Number.isFinite(step.visitors) ? Math.max(0, step.visitors) : 0;
      const previousStep = index > 0 ? cleanData.conversionSteps[index - 1] : null;
      const previousVisitors = previousStep && typeof previousStep.visitors === 'number' && Number.isFinite(previousStep.visitors)
        ? Math.max(0, previousStep.visitors)
        : visitors;

      const dropoff = Math.max(0, previousVisitors - visitors);
      const dropoffRate = previousVisitors > 0 ? dropoff / previousVisitors : 0;
      const firstStepVisitors = cleanData.conversionSteps[0]?.visitors;
      const conversionRate = index > 0 && typeof firstStepVisitors === 'number' && firstStepVisitors > 0
        ? visitors / firstStepVisitors
        : 1;

      return {
        ...step,
        visitors,
        dropoff,
        dropoffRate: Number.isFinite(dropoffRate) ? dropoffRate : 0,
        conversionRate: Number.isFinite(conversionRate) ? conversionRate : 0
      };
    });
  }, [cleanData?.conversionSteps]);
  
  // Create a heatmap data structure based on real activity data
  const heatmapData = useMemo(() => {
    // We'll derive heatmap data from hourly and daily activity patterns
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const hours = Array.from({ length: 24 }, (_, i) => i);
    
    // Get total visits for normalization
    let totalVisits = 0;
    if (cleanData?.hourlyActivity) {
      totalVisits = cleanData.hourlyActivity.reduce((sum, item) => sum + item.visits, 0);
    }
    
    // Get relative daily distribution
    const dayFactors = new Map();
    if (cleanData?.weekdayActivity && cleanData.weekdayActivity.length > 0) {
      const totalDayVisits = cleanData.weekdayActivity.reduce((sum, item) => sum + item.visits, 0);
      cleanData.weekdayActivity.forEach(item => {
        dayFactors.set(item.day, totalDayVisits > 0 ? item.visits / totalDayVisits : 0);
      });
    } else {
      // Equal distribution if no data
      days.forEach(day => dayFactors.set(day, 1/7));
    }
    
    // Get relative hourly distribution
    const hourFactors = new Map();
    if (cleanData?.hourlyActivity && cleanData.hourlyActivity.length > 0) {
      cleanData.hourlyActivity.forEach(item => {
        hourFactors.set(item.hour, totalVisits > 0 ? item.visits / totalVisits : 0);
      });
    } else {
      // Equal distribution if no data
      hours.forEach(hour => hourFactors.set(hour, 1/24));
    }
    
    // Generate heatmap based on the patterns
    return days.flatMap(day => 
      hours.map(hour => {
        const dayFactor = dayFactors.get(day) || 0;
        const hourFactor = hourFactors.get(hour) || 0;
        
        // Use real data factors without random variation
        const value = Math.round(100 * dayFactor * hourFactor * 30); // Scale up for visibility
        
        return {
          day,
          day_index: days.indexOf(day),
          hour,
          value: Math.min(100, Math.max(0, value)) // Clamp to 0-100 range
        };
      })
    );
  }, [cleanData?.hourlyActivity, cleanData?.weekdayActivity]);
  
  // Format percentages with null/undefined handling
  const formatPercent = (value: number | undefined) => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0.0%';
    }
    return `${(value * 100).toFixed(1)}%`;
  };
  
  // Format time with null/undefined handling
  const formatTime = (seconds: number | undefined) => {
    if (seconds === undefined || seconds === null || isNaN(seconds)) {
      return '0m 0s';
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}m ${secs}s`;
  };
  
  // Skeleton loader for the insights component
  if (isLoading) {
    return (
      <Card className={`border shadow-md ${className}`}>
        <CardHeader>
          <div className="h-6 w-40 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-64 bg-muted/60 animate-pulse rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="h-[300px] w-full bg-muted/50 animate-pulse rounded"></div>
            <div className="grid grid-cols-2 gap-4">
              <div className="h-[150px] bg-muted/30 animate-pulse rounded"></div>
              <div className="h-[150px] bg-muted/30 animate-pulse rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={`border shadow-md ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-primary" />
          Visitor Behavior Insights
        </CardTitle>
        <CardDescription>
          Advanced analytics and patterns in visitor behavior
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="patterns" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="patterns" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <Activity className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Behavior Patterns</span>
              <span className="sm:hidden">Patterns</span>
            </TabsTrigger>
            <TabsTrigger value="funnel" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Conversion Funnel</span>
              <span className="sm:hidden">Funnel</span>
            </TabsTrigger>
            <TabsTrigger value="anomalies" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Anomalies</span>
              <span className="sm:hidden">Issues</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="patterns" className="space-y-6">
            {/* Visitor Activity Heatmap - Mobile Optimized */}
            <div>
              <h3 className="text-lg font-medium mb-3">Activity Heatmap by Hour & Day</h3>
              <div className="h-[350px] overflow-x-auto overflow-y-hidden">
                {/* Mobile-responsive heatmap with proper scrolling */}
                <div className="grid grid-cols-[auto_repeat(7,minmax(50px,1fr))] sm:grid-cols-[auto_repeat(7,minmax(60px,1fr))] gap-1 h-full min-w-[400px] sm:min-w-[500px]">
                  <div className="flex flex-col justify-between text-xs text-muted-foreground pt-6 pb-2">
                    <div>00:00</div>
                    <div>06:00</div>
                    <div>12:00</div>
                    <div>18:00</div>
                    <div>23:00</div>
                  </div>

                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
                    <div key={day} className="flex flex-col">
                      <div className="text-center text-xs font-medium mb-1">{day}</div>
                      <div className="flex-1 grid grid-rows-24 gap-0.5">
                        {Array.from({ length: 24 }, (_, hour) => {
                          const dayIndex = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].indexOf(day);
                          const dayName = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayIndex];
                          const heatmapItem = heatmapData.find(item =>
                            item.day === dayName && item.hour === hour
                          );
                          const value = Number.isFinite(heatmapItem?.value) ? heatmapItem?.value || 0 : 0;
                          const opacity = Math.max(0.1, Math.min(0.9, value / 100));
                          
                          return (
                            <TooltipProvider key={hour}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div 
                                    className="w-full h-full bg-primary rounded-sm"
                                    style={{ opacity }}
                                  ></div>
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                  <div className="text-xs">
                                    <div className="font-medium">{dayName}, {hour}:00</div>
                                    <div>{value} visitors</div>
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Activity by Hour and Day - Mobile Optimized */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="overflow-hidden">
                <h3 className="text-lg font-medium mb-3">Activity by Hour of Day</h3>
                <div className="h-[200px] w-full min-w-0">
                  <BarChart
                    data={processedHourlyData}
                    index="hour_label"
                    categories={['visits']}
                    colors={['#3b82f6']}
                    valueFormatter={(value) => `${Number.isFinite(value) ? value.toLocaleString() : '0'} visits`}
                    showAnimation={true}
                  />
                </div>
              </div>

              <div className="overflow-hidden">
                <h3 className="text-lg font-medium mb-3">Activity by Day of Week</h3>
                <div className="h-[200px] w-full min-w-0">
                  <BarChart
                    data={processedWeekdayData}
                    index="day_short"
                    categories={['visits']}
                    colors={['#8b5cf6']}
                    valueFormatter={(value) => `${Number.isFinite(value) ? value.toLocaleString() : '0'} visits`}
                    showAnimation={true}
                  />
                </div>
              </div>
            </div>
            
            {/* Session & Bounce Metrics - Mobile Optimized */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-slate-50 dark:bg-slate-900/50">
                <CardHeader className="py-4">
                  <CardTitle className="text-base flex items-center gap-2">
                    <MousePointerClick className="h-4 w-4 text-amber-500" />
                    Bounce Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl sm:text-3xl font-bold">
                      {formatPercent(cleanData.bounceRate)}
                    </div>
                    <Badge variant={cleanData.bounceRate > 0.7 ? "destructive" : cleanData.bounceRate > 0.5 ? "outline" : "secondary"}>
                      {cleanData.bounceRate > 0.7 ? "High" : cleanData.bounceRate > 0.5 ? "Average" : "Good"}
                    </Badge>
                  </div>
                  <div className="mt-4 h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div 
                      className={`h-full rounded-full ${
                        cleanData.bounceRate > 0.7 ? "bg-red-500" : 
                        cleanData.bounceRate > 0.5 ? "bg-amber-500" : 
                        "bg-green-500"
                      }`}
                      style={{ width: `${cleanData.bounceRate * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    {cleanData.bounceRate > 0.7 
                      ? "Higher than average. Consider improving landing pages."
                      : cleanData.bounceRate > 0.5
                        ? "Average bounce rate. Room for improvement."
                        : "Good engagement. Visitors are exploring your site."
                    }
                  </p>
                </CardContent>
              </Card>
              
              <Card className="bg-slate-50 dark:bg-slate-900/50">
                <CardHeader className="py-4">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Clock className="h-4 w-4 text-emerald-500" />
                    Session Duration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl sm:text-3xl font-bold">
                      {formatTime(cleanData.averageSessionTime)}
                    </div>
                    <Badge variant={cleanData.averageSessionTime < 60 ? "destructive" : cleanData.averageSessionTime < 180 ? "outline" : "secondary"}>
                      {cleanData.averageSessionTime < 60 ? "Short" : cleanData.averageSessionTime < 180 ? "Average" : "Good"}
                    </Badge>
                  </div>
                  <div className="mt-4 h-2 w-full bg-muted rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-emerald-500 rounded-full"
                      style={{ width: `${Math.min(100, (cleanData.averageSessionTime / 300) * 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    {cleanData.averageSessionTime < 60
                      ? "Users aren't staying long. Consider improving content engagement."
                      : cleanData.averageSessionTime < 180
                        ? "Average session time. Content is moderately engaging."
                        : "Great engagement. Users are spending good time on your site."
                    }
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="funnel" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Conversion Funnel</h3>
              <div className="space-y-2">
                {funnelData.map((step, index) => (
                  <div key={step.step} className="flex flex-col">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-1 gap-2">
                      <div className="flex items-center gap-2">
                        <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-sm font-medium">
                          {index + 1}
                        </span>
                        <span className="font-medium text-sm sm:text-base">{step.step}</span>
                      </div>
                      <div className="flex items-center gap-3 ml-8 sm:ml-0">
                        <span className="text-xs sm:text-sm font-medium">
                          {Number.isFinite(step.visitors) ? step.visitors.toLocaleString() : '0'} visitors
                        </span>
                        {index > 0 && (
                          <Badge variant={step.dropoffRate > 0.5 ? "destructive" : "outline"} className="text-xs">
                            {formatPercent(step.conversionRate)} conversion
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="h-12 relative mb-4">
                      <div className="absolute inset-0 bg-muted rounded-md"></div>
                      <div 
                        className="absolute inset-y-0 left-0 bg-primary rounded-md"
                        style={{ 
                          width: `${Math.max(
                            5,
                            (step.visitors / funnelData[0].visitors) * 100
                          )}%` 
                        }}
                      ></div>
                      
                      {index < funnelData.length - 1 && (
                        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                          <div className="flex flex-col items-center">
                            <ArrowRight className="h-4 w-4 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {formatPercent(step.dropoffRate)} drop off
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-slate-50 dark:bg-slate-900/50 rounded-lg">
                <h4 className="text-sm font-medium mb-2">Funnel Insights</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <TrendingDown className="h-4 w-4 text-red-500 mt-0.5" />
                    <span>
                      The biggest drop-off occurs at the <span className="font-medium">{funnelData.reduce((prev, current, i) => 
                        i > 0 && current.dropoffRate > funnelData[prev].dropoffRate ? i : prev, 0)}</span> step
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>
                      The highest conversion is <span className="font-medium">{
                        formatPercent(funnelData.reduce((max, step) => 
                          Math.max(max, step.conversionRate), 0))
                      }</span> between steps
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="anomalies" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Detected Anomalies</h3>
              
              {cleanData.anomalies.length === 0 ? (
                <div className="p-8 border rounded-lg bg-slate-50 dark:bg-slate-900/50 text-center">
                  <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">No anomalies detected in the selected time period</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {cleanData.anomalies.map((anomaly, index) => (
                    <Card key={index} className={`border-l-4 ${
                      anomaly.percentChange > 0.5 ? "border-l-green-500" : 
                      anomaly.percentChange < -0.5 ? "border-l-red-500" : 
                      "border-l-amber-500"
                    }`}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{anomaly.metric}</h4>
                            <p className="text-sm text-muted-foreground">{anomaly.date}</p>
                          </div>
                          <Badge variant={
                            anomaly.percentChange > 0.5 ? "success" : 
                            anomaly.percentChange < -0.5 ? "destructive" : 
                            "outline"
                          }>
                            {anomaly.percentChange > 0 ? "+" : ""}
                            {formatPercent(anomaly.percentChange)}
                          </Badge>
                        </div>
                        
                        <div className="mt-2 flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-sm">Expected: {anomaly.expected.toLocaleString()}</span>
                            <ArrowRight className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm font-medium">Actual: {anomaly.value.toLocaleString()}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 