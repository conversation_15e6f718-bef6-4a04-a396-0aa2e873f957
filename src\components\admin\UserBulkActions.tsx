'use client';

import { useState } from 'react';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { 
  CheckCircle, 
  ChevronDown, 
  Loader2, 
  Shield, 
  Trash2, 
  UserCheck, 
  UserX 
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import BulkDeleteDialog from './BulkDeleteDialog';
import ConfirmationDialog, { useConfirmationDialog } from './ConfirmationDialog';

interface UserBulkActionsProps {
  selectedUsers: string[];
  onActionComplete: () => void;
}

export default function UserBulkActions({ 
  selectedUsers, 
  onActionComplete 
}: UserBulkActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const hasSelection = selectedUsers.length > 0;
  const { user: currentUser } = useAuth();
  const { openDialog, ConfirmationDialog } = useConfirmationDialog();

  // Handle bulk role change
  const handleBulkRoleChange = async (newRole: string) => {
    if (!hasSelection) return;
    
    openDialog({
      title: 'Change User Roles',
      description: `Are you sure you want to change ${selectedUsers.length} user(s) to ${newRole} role? This will modify their permissions and access levels.`,
      confirmText: `Change to ${newRole.toUpperCase()}`,
      variant: 'default',
      onConfirm: () => performBulkRoleChange(newRole)
    });
  };

  const performBulkRoleChange = async (newRole: string) => {
    
    setIsLoading(true);
    
    try {
      // Make API request to update roles
      const response = await fetch('/api/admin/users/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          action: 'changeRole',
          role: newRole
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user roles');
      }
      
      toast({
        title: 'Roles Updated',
        description: `Successfully updated ${selectedUsers.length} user(s) to ${newRole} role.`,
        variant: 'success'
      });
      
      onActionComplete();
    } catch (error) {
      console.error('Error updating roles:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle bulk verification status change
  const handleBulkVerificationChange = async (verified: boolean) => {
    if (!hasSelection) return;
    
    const action = verified ? 'verify' : 'unverify';
    const actionDescription = verified 
      ? 'This will mark the selected users as verified and grant them full access.'
      : 'This will remove verification status from the selected users and may restrict their access.';
    
    openDialog({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Users`,
      description: `Are you sure you want to ${action} ${selectedUsers.length} user(s)? ${actionDescription}`,
      confirmText: `${action.charAt(0).toUpperCase() + action.slice(1)} Users`,
      variant: 'default',
      onConfirm: () => performBulkVerificationChange(verified)
    });
  };

  const performBulkVerificationChange = async (verified: boolean) => {
    
    setIsLoading(true);
    
    try {
      // Make API request to update verification status
      const response = await fetch('/api/admin/users/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          action: 'changeVerification',
          verified
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update verification status');
      }
      
      toast({
        title: 'Verification Status Updated',
        description: `Successfully ${verified ? 'verified' : 'unverified'} ${selectedUsers.length} user(s).`,
        variant: 'success'
      });
      
      onActionComplete();
    } catch (error) {
      console.error('Error updating verification status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle bulk deletion
  const handleBulkDelete = () => {
    if (!hasSelection) return;
    setDeleteDialogOpen(true);
  };

  const performBulkDelete = async () => {
    
    setIsLoading(true);
    
    try {
      // Make API request to delete users
      // Add current user ID as query parameter for authentication fallback
      const currentUserId = currentUser?.id;
      const url = currentUserId 
        ? `/api/admin/users/bulk?userId=${currentUserId}`
        : '/api/admin/users/bulk';
        
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete users');
      }
      
      toast({
        title: 'Users Deleted',
        description: `Successfully deleted ${selectedUsers.length} user(s).`,
        variant: 'success'
      });
      
      onActionComplete();
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting users:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          disabled={!hasSelection || isLoading}
          className="flex items-center h-9 sm:h-10 px-3 sm:px-4 text-sm font-medium w-full sm:w-auto"
        >
          {isLoading ? (
            <Loader2 className="mr-1 sm:mr-2 h-4 w-4 animate-spin flex-shrink-0" />
          ) : (
            <CheckCircle className="mr-1 sm:mr-2 h-4 w-4 flex-shrink-0" />
          )}
          <span className="truncate">Bulk Actions</span>
          <ChevronDown className="ml-1 sm:ml-2 h-4 w-4 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 sm:w-56">
        <DropdownMenuLabel className="text-xs sm:text-sm">
          {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuLabel>Change Role</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('user')}>
          <UserCheck className="mr-2 h-4 w-4" />
          Set as User
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('moderator')}>
          <Shield className="mr-2 h-4 w-4" />
          Set as Moderator
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('admin')}>
          <Shield className="mr-2 h-4 w-4" />
          Set as Admin
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuLabel>Verification</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => handleBulkVerificationChange(true)}>
          <CheckCircle className="mr-2 h-4 w-4" />
          Mark as Verified
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkVerificationChange(false)}>
          <UserX className="mr-2 h-4 w-4" />
          Mark as Unverified
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleBulkDelete}
          className="text-red-500 focus:text-red-500"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Selected
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    {/* Confirmation Dialogs */}
    <ConfirmationDialog />
    
    <BulkDeleteDialog
      open={deleteDialogOpen}
      onOpenChange={setDeleteDialogOpen}
      onConfirm={performBulkDelete}
      itemCount={selectedUsers.length}
      itemType="user"
      isLoading={isLoading}
      warningMessage="This will permanently remove all user data including profiles, watch history, preferences, and activity logs."
    />
  </>
  );
}
