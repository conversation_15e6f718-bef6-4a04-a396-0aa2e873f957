import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User'; // Static import
import { IUser } from '@/models/User'; // Import IUser interface
import { ensureMongooseConnection } from '@/lib/mongodb';

interface AdminCheckResult {
  isAdmin: boolean;
  user?: IUser;
  message?: string;
}

/**
 * Middleware to check if a user has admin privileges
 * This version uses cookie-based authentication
 */
export async function adminMiddleware(req: NextRequest): Promise<AdminCheckResult> {
  try {
    // Get the userId from cookies
    const userId = req.cookies.get('userId')?.value;

    if (!userId) {
      return { isAdmin: false, message: 'User not authenticated' };
    }

    // Check the database
    await ensureMongooseConnection();
    // Use statically imported User model

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return { isAdmin: false, message: 'User not found in database' };
    }

    // Check if user has admin role
    const isAdmin = user.role === 'admin' || user.role === 'superadmin';

    return {
      isAdmin,
      user,
      message: isAdmin ? undefined : 'User does not have admin privileges'
    };
  } catch (error) {
    console.error('Error in admin middleware:', error);
    return { isAdmin: false, message: 'Error checking admin status' };
  }
}

/**
 * Middleware to authenticate user
 * This version uses cookie-based authentication
 */
export async function authMiddleware(req: NextRequest) {
  try {
    // Get the userId from cookies
    const userId = req.cookies.get('userId')?.value;

    if (!userId) {
      return NextResponse.json({ message: 'Access denied' }, { status: 401 });
    }

    // Check the database
    await ensureMongooseConnection();
    // Use statically imported User model

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json({ message: 'Access denied' }, { status: 401 });
    }

    return { isAuthenticated: true, user };
  } catch (error) {
    console.error('Error in auth middleware:', error);
    return { isAuthenticated: false, message: 'Error checking authentication status' };
  }
}

// Check if user is admin
export async function isAdmin(request: NextRequest) {
  try {
    // Get the userId from cookies first, then from query parameters as fallback
    let userId = request.cookies.get('userId')?.value;

    if (!userId) {
      // Fallback to query parameters
      const { searchParams } = new URL(request.url);
      userId = searchParams.get('userId') || undefined;
    }

    if (!userId) {
      return {
        isAuthorized: false,
        message: 'No user ID found in cookies or query parameters'
      };
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      return {
        isAuthorized: false,
        message: 'User not found'
      };
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return {
        isAuthorized: false,
        message: 'User does not have admin privileges'
      };
    }

    return {
      isAuthorized: true,
      user
    };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return {
      isAuthorized: false,
      message: 'Server error checking authorization'
    };
  }
}
