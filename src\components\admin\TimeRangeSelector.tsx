'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, CalendarDays, CalendarRange } from 'lucide-react';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format, subDays, isEqual, startOfDay } from 'date-fns';
import { DateRange } from 'react-day-picker';

type TimeRange = {
  from: Date | null;
  to: Date | null;
};

type PresetPeriod = '7d' | '30d' | '90d' | 'ytd' | 'all' | 'custom';

interface TimeRangeSelectorProps {
  onChange: (range: TimeRange) => void;
  initialRange?: TimeRange;
  className?: string;
}

export default function TimeRangeSelector({
  onChange,
  initialRange,
  className = '',
}: TimeRangeSelectorProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<PresetPeriod>('30d');
  const [dateRange, setDateRange] = useState<TimeRange>(
    initialRange || {
      from: subDays(new Date(), 30),
      to: new Date(),
    }
  );

  // Pre-defined date ranges - wrapped in useMemo to prevent recreation on every render
  const dateRanges = useMemo(() => ({
    '7d': {
      from: subDays(new Date(), 7),
      to: new Date()
    },
    '30d': {
      from: subDays(new Date(), 30),
      to: new Date()
    },
    '90d': {
      from: subDays(new Date(), 90),
      to: new Date()
    },
    'ytd': {
      from: new Date(new Date().getFullYear(), 0, 1),
      to: new Date()
    },
    'all': {
      from: null,
      to: null
    },
  }), []);

  // Initialize with default or provided date range
  useEffect(() => {
    if (initialRange) {
      setDateRange(initialRange);
      
      // Try to match the initial range with a preset
      const matchedPreset = Object.entries(dateRanges).find(([key, range]) => {
        if (!range.from && !initialRange.from) return true;
        if (!range.from || !initialRange.from) return false;
        
        return isEqual(
          startOfDay(range.from),
          startOfDay(initialRange.from)
        ) && isEqual(
          startOfDay(range.to || new Date()),
          startOfDay(initialRange.to || new Date())
        );
      });
      
      if (matchedPreset) {
        setSelectedPeriod(matchedPreset[0] as PresetPeriod);
      } else {
        setSelectedPeriod('custom');
      }
    }
  }, [initialRange, dateRanges]); // Dependencies for date range matching

  // Handle preset periods
  const handlePresetPeriod = (period: PresetPeriod) => {
    if (period === 'custom') {
      setSelectedPeriod('custom');
      return;
    }

    const newRange = dateRanges[period];
    setDateRange(newRange);
    setSelectedPeriod(period);
    onChange(newRange);
  };

  // Handle custom date range selection
  const handleDateRangeChange = (range: DateRange | undefined) => {
    const newRange: TimeRange = {
      from: range?.from || null,
      to: range?.to || null
    };
    
    setDateRange(newRange);
    setSelectedPeriod('custom');
    onChange(newRange);
  };

  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return format(date, 'MMM d, yyyy');
  };

  // Display text for the current selection
  const displayText = selectedPeriod === 'custom'
    ? `${formatDate(dateRange.from)} - ${formatDate(dateRange.to)}`
    : selectedPeriod === '7d'
      ? 'Last 7 days'
      : selectedPeriod === '30d'
        ? 'Last 30 days'
        : selectedPeriod === '90d'
          ? 'Last 90 days'
          : selectedPeriod === 'ytd'
            ? 'Year to date'
            : 'All time';

  return (
    <div className={`flex flex-col sm:flex-row items-stretch sm:items-center gap-2 ${className}`}>
      {/* Mobile: Stack buttons in rows, Desktop: Single row */}
      <div className="flex flex-wrap sm:flex-nowrap bg-muted rounded-md border shadow-sm overflow-hidden">
        <Button
          variant={selectedPeriod === '7d' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => handlePresetPeriod('7d')}
          className="rounded-none h-9 px-2 sm:px-3 flex-1 sm:flex-none min-w-0 text-xs sm:text-sm"
        >
          7D
        </Button>
        <Button
          variant={selectedPeriod === '30d' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => handlePresetPeriod('30d')}
          className="rounded-none h-9 px-2 sm:px-3 flex-1 sm:flex-none min-w-0 text-xs sm:text-sm"
        >
          30D
        </Button>
        <Button
          variant={selectedPeriod === '90d' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => handlePresetPeriod('90d')}
          className="rounded-none h-9 px-2 sm:px-3 flex-1 sm:flex-none min-w-0 text-xs sm:text-sm"
        >
          90D
        </Button>
        <Button
          variant={selectedPeriod === 'ytd' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => handlePresetPeriod('ytd')}
          className="rounded-none h-9 px-2 sm:px-3 flex-1 sm:flex-none min-w-0 text-xs sm:text-sm"
        >
          YTD
        </Button>
        <Button
          variant={selectedPeriod === 'all' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => handlePresetPeriod('all')}
          className="rounded-none h-9 px-2 sm:px-3 flex-1 sm:flex-none min-w-0 text-xs sm:text-sm"
        >
          All
        </Button>
      </div>

      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`h-9 flex items-center gap-2 w-full sm:w-auto justify-center sm:justify-start ${selectedPeriod === 'custom' ? 'bg-primary/10 border-primary text-primary' : ''}`}
          >
            <CalendarRange className="h-4 w-4" />
            <span className="hidden sm:inline text-xs sm:text-sm">{displayText}</span>
            <span className="sm:hidden text-xs">Custom</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <DateRangePicker
            dateRange={dateRange}
            onSelect={handleDateRangeChange}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
} 