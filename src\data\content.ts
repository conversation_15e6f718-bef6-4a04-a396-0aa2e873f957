// Content data types and live data functions for StreamVista
import { getPopularMovies, getPopularTVShows, MappedContent } from '@/lib/tmdb-api';

export interface Genre {
  id: number;
  name: string;
}

export interface IContent {
  id: string | number;
  title: string;
  type: 'movie' | 'show';
  year: string;
  image: string;
  posterPath?: string;
  backdropPath?: string;
  overview?: string;
  genres?: string[];
  runtime?: number;
  rating?: number;
  seasons?: number;
  episodes?: number;
  creator?: string;
  tmdbId?: string;
  imdbId?: string;
  awards?: boolean;
}

export type ContentType = IContent;

// Interface for continue watching API response
interface ContinueWatchingItem {
  id: string;
  title: string;
  image: string;
  type: 'show' | 'movie';
  progress: number;
  episode?: string;
  season?: number;
  currentTime: number;
  duration: number;
  timestamp: string;
}

// Convert TMDb data to IContent format
function convertTMDbToIContent(tmdbContent: MappedContent[]): IContent[] {
  return tmdbContent.map(item => ({
    id: item.id,
    title: item.title,
    type: item.mediaType === 'tv' ? 'show' : 'movie',
    year: item.year?.toString() || new Date(item.releaseDate || '').getFullYear().toString() || '',
    image: item.posterUrl,
    posterPath: item.posterUrl,
    backdropPath: item.backdropUrl,
    overview: item.overview,
    genres: [], // Will be populated from genre IDs if needed
    rating: item.voteAverage,
    tmdbId: item.tmdbId,
    imdbId: item.imdbId
  }));
}

// Live data functions that fetch from TMDb API
export async function getPopularMoviesData(): Promise<IContent[]> {
  try {
    const tmdbMovies = await getPopularMovies();
    return convertTMDbToIContent(tmdbMovies);
  } catch (error) {
    console.error('Error fetching popular movies:', error);
    return [];
  }
}

export async function getPopularShowsData(): Promise<IContent[]> {
  try {
    const tmdbShows = await getPopularTVShows();
    return convertTMDbToIContent(tmdbShows);
  } catch (error) {
    console.error('Error fetching popular shows:', error);
    return [];
  }
}

// Continue watching functionality - fetches user's actual continue watching data
export async function getContinueWatchingData(userId?: string): Promise<IContent[]> {
  if (!userId) {
    return [];
  }

  try {
    // Fetch from your continue watching API
    const response = await fetch(`/api/user/continue-watching?userId=${userId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch continue watching data');
    }

    const data = await response.json();

    // Convert API response to IContent format
    if (data.success && data.items) {
      return data.items.map((item: ContinueWatchingItem) => ({
        id: item.id,
        title: item.title,
        type: item.type,
        year: '', // Will be populated from content details if needed
        image: item.image,
        posterPath: item.image,
        overview: '',
        genres: [],
        progress: item.progress,
        currentTime: item.currentTime,
        duration: item.duration,
        season: item.season,
        episode: item.episode
      }));
    }

    return [];
  } catch (error) {
    console.error('Error fetching continue watching data:', error);
    return [];
  }
}

// Fallback static data for build time (will be replaced by live data at runtime)
export const popularMovies: IContent[] = [];
export const popularShows: IContent[] = [];
export const continueWatching: IContent[] = [];

// Export all content arrays (empty by default, populated by live data)
export const allContent: IContent[] = [];

// Genre definitions
export const genres: Genre[] = [
  { id: 28, name: 'Action' },
  { id: 12, name: 'Adventure' },
  { id: 16, name: 'Animation' },
  { id: 35, name: 'Comedy' },
  { id: 80, name: 'Crime' },
  { id: 99, name: 'Documentary' },
  { id: 18, name: 'Drama' },
  { id: 10751, name: 'Family' },
  { id: 14, name: 'Fantasy' },
  { id: 36, name: 'History' },
  { id: 27, name: 'Horror' },
  { id: 10402, name: 'Music' },
  { id: 9648, name: 'Mystery' },
  { id: 10749, name: 'Romance' },
  { id: 878, name: 'Science Fiction' },
  { id: 10770, name: 'TV Movie' },
  { id: 53, name: 'Thriller' },
  { id: 10752, name: 'War' },
  { id: 37, name: 'Western' }
];
