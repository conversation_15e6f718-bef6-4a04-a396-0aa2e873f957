'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LineC<PERSON> } from '@/components/ui/charts';
import { VisitorDistribution } from '@/hooks/useVisitorData';
import { 
  LayoutGrid, 
  BarChart3, 
  Pie<PERSON><PERSON> as PieChartIcon,
  ArrowUpRight,
  Info,
  Monitor,
  Globe,
  Chrome
} from 'lucide-react';

interface VisitorChartsProps {
  distributions: {
    device: VisitorDistribution[];
    browser: VisitorDistribution[];
    os: VisitorDistribution[];
    country: VisitorDistribution[];
  };
  dailyVisitors: {
    _id: string;
    count: number;
  }[];
  isLoading: boolean;
}

export default function VisitorCharts({ distributions, dailyVisitors, isLoading }: VisitorChartsProps) {
  const [chartView, setChartView] = useState<'combined' | 'detail'>('combined');
  const [distributionType, setDistributionType] = useState('device');
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Check if there's any data
  const hasData = distributions.device.length > 0 ||
                 distributions.browser.length > 0 ||
                 distributions.os.length > 0 ||
                 distributions.country.length > 0 ||
                 dailyVisitors.length > 0;

  // Process data for charts
  const deviceData = distributions.device.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const browserData = distributions.browser.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const osData = distributions.os.slice(0, 5).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  const countryData = distributions.country.slice(0, 8).map(item => ({
    name: item._id || 'Unknown',
    value: item.count
  }));

  // Process daily visitor data
  const dailyData = dailyVisitors.map(item => ({
    date: item._id,
    visitors: item.count
  }));

  // Distribution icon mapping
  const distributionIcons = {
    device: <Monitor className="h-4 w-4" />,
    browser: <Chrome className="h-4 w-4" />,
    os: <LayoutGrid className="h-4 w-4" />,
    country: <Globe className="h-4 w-4" />
  };

  // If there's no data and not loading, show empty state
  if (!hasData && !isLoading) {
    return (
      <Card className="bg-vista-dark-lighter border-vista-light/10 mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-vista-light flex items-center justify-between">
            <span className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-vista-blue" />
              Visitor Analytics
            </span>
          </CardTitle>
          <CardDescription>Visual representation of visitor metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-center text-vista-light/50">
              <p>No visitor data available yet</p>
              <p className="text-sm mt-1">Charts will appear as visitors browse your site</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-vista-dark-lighter border-vista-light/10 mb-6">
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <CardTitle className="text-vista-light flex items-center gap-2 text-lg sm:text-xl">
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-vista-blue" />
              Visitor Analytics
            </CardTitle>
            <CardDescription className="text-sm">Visual representation of visitor metrics</CardDescription>
          </div>

          <div className="flex items-center gap-2 self-start sm:self-auto">
            <Tabs value={chartView} onValueChange={(value) => setChartView(value as 'combined' | 'detail')} className="w-auto">
              <TabsList className="h-8 sm:h-9 p-1">
                <TabsTrigger value="combined" className="h-6 sm:h-7 px-2 sm:px-3 text-xs sm:text-sm">
                  <LayoutGrid className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                  <span className="hidden xs:inline">Dashboard</span>
                  <span className="xs:hidden">Dash</span>
                </TabsTrigger>
                <TabsTrigger value="detail" className="h-6 sm:h-7 px-2 sm:px-3 text-xs sm:text-sm">
                  <BarChart3 className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                  <span className="hidden xs:inline">Details</span>
                  <span className="xs:hidden">Detail</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="h-[300px] sm:h-[400px] w-full bg-vista-dark/50 animate-pulse rounded"></div>
        ) : (
          <>
            {chartView === 'combined' ? (
              <div className="space-y-6 lg:grid lg:grid-cols-3 lg:gap-6 lg:space-y-0">
                {/* Enhanced Trends Chart - Mobile: Full width, Desktop: 2/3 width */}
                <div className="lg:col-span-2 bg-gradient-to-br from-vista-dark/30 to-vista-dark/10 border border-vista-light/10 rounded-xl p-3 sm:p-4 backdrop-blur-sm">
                  <div className="flex flex-col gap-3 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className="p-1.5 sm:p-2 bg-blue-500/20 rounded-lg">
                          <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 text-blue-400" />
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base font-semibold text-vista-light">Daily Visitor Trends</h3>
                        </div>
                      </div>
                      <div className="text-xs sm:text-sm text-blue-400/80 font-medium">
                        30 days
                      </div>
                    </div>
                    <p className="text-xs text-vista-light/60 sm:hidden">Visitor activity over time</p>
                  </div>
                  <div className="h-[250px] sm:h-[300px] lg:h-[320px]">
                    <LineChart
                      data={dailyData}
                      index="date"
                      categories={['visitors']}
                      colors={['#3b82f6']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showLegend={false}
                      showGridLines={true}
                      showAnimation={true}
                      curveType="smooth"
                    />
                  </div>
                </div>

                {/* Enhanced Distribution Charts - Mobile: Full width, Desktop: 1/3 width */}
                <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-xl p-3 sm:p-4 backdrop-blur-sm">
                  <div className="flex flex-col gap-3 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 sm:p-2 bg-purple-500/20 rounded-lg">
                          {distributionIcons[distributionType as keyof typeof distributionIcons]}
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base font-semibold text-vista-light capitalize">
                            {distributionType}
                          </h3>
                        </div>
                      </div>
                      <select
                        value={distributionType}
                        onChange={(e) => setDistributionType(e.target.value)}
                        className="text-xs sm:text-sm bg-vista-dark/80 border border-vista-light/20 rounded-lg px-2 sm:px-3 py-1.5 text-vista-light/80 focus:border-purple-500/50 focus:outline-none transition-colors"
                      >
                        <option value="device">Device</option>
                        <option value="browser">Browser</option>
                        <option value="os">OS</option>
                        <option value="country">Country</option>
                      </select>
                    </div>
                    <p className="text-xs text-vista-light/60 sm:hidden">Top categories</p>
                  </div>

                  <div className="h-[280px] sm:h-[320px] lg:h-[360px]">
                    <div className="w-full h-full">
                      {/* Mobile: Smaller pie chart with adjusted legend */}
                      <div className="block sm:hidden h-full">
                        <PieChart
                          data={
                            distributionType === 'country' ? countryData.slice(0, 4) :
                            distributionType === 'device' ? deviceData.slice(0, 4) :
                            distributionType === 'browser' ? browserData.slice(0, 4) : osData.slice(0, 4)
                          }
                          category="value"
                          index="name"
                          colors={['#8b5cf6', '#3b82f6', '#ec4899', '#f97316']}
                          valueFormatter={(value) => `${value.toLocaleString()}`}
                          showAnimation={true}
                          showLegend={true}
                          innerRadius={10}
                          outerRadius={45}
                        />
                      </div>
                      {/* Desktop: Full pie chart */}
                      <div className="hidden sm:block h-full">
                        <PieChart
                          data={
                            distributionType === 'country' ? countryData :
                            distributionType === 'device' ? deviceData :
                            distributionType === 'browser' ? browserData : osData
                          }
                          category="value"
                          index="name"
                          colors={['#8b5cf6', '#3b82f6', '#ec4899', '#f97316', '#10b981']}
                          valueFormatter={(value) => `${value.toLocaleString()}`}
                          showAnimation={true}
                          showLegend={true}
                          innerRadius={20}
                          outerRadius={70}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-[300px] sm:h-[400px]">
                <Tabs defaultValue="device" className="w-full h-full">
                  <div className="mb-4 overflow-x-auto">
                    <TabsList className="grid grid-cols-4 h-9 sm:h-10 w-full min-w-[320px]">
                      <TabsTrigger value="device" className="flex items-center gap-1 text-xs sm:text-sm px-1 sm:px-3">
                        <Monitor className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
                        <span className="hidden xs:inline">Device</span>
                        <span className="xs:hidden">Dev</span>
                      </TabsTrigger>
                      <TabsTrigger value="browser" className="flex items-center gap-1 text-xs sm:text-sm px-1 sm:px-3">
                        <Chrome className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
                        <span className="hidden xs:inline">Browser</span>
                        <span className="xs:hidden">Brow</span>
                      </TabsTrigger>
                      <TabsTrigger value="os" className="flex items-center gap-1 text-xs sm:text-sm px-1 sm:px-3">
                        <LayoutGrid className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
                        <span className="hidden xs:inline">OS</span>
                        <span className="xs:hidden">OS</span>
                      </TabsTrigger>
                      <TabsTrigger value="country" className="flex items-center gap-1 text-xs sm:text-sm px-1 sm:px-3">
                        <Globe className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
                        <span className="hidden xs:inline">Country</span>
                        <span className="xs:hidden">Loc</span>
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="device" className="h-[250px] sm:h-[350px]">
                    <PieChart
                      data={deviceData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                      showLegend={true}
                      innerRadius={isMobile ? 15 : 20}
                      outerRadius={isMobile ? 50 : 70}
                    />
                  </TabsContent>

                  <TabsContent value="browser" className="h-[250px] sm:h-[350px]">
                    <PieChart
                      data={browserData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                      showLegend={true}
                      innerRadius={isMobile ? 15 : 20}
                      outerRadius={isMobile ? 50 : 70}
                    />
                  </TabsContent>

                  <TabsContent value="os" className="h-[250px] sm:h-[350px]">
                    <PieChart
                      data={osData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                      showLegend={true}
                      innerRadius={isMobile ? 15 : 20}
                      outerRadius={isMobile ? 50 : 70}
                    />
                  </TabsContent>

                  <TabsContent value="country" className="h-[250px] sm:h-[350px]">
                    <PieChart
                      data={countryData}
                      category="value"
                      index="name"
                      colors={['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981']}
                      valueFormatter={(value) => `${value.toLocaleString()} visitors`}
                      showAnimation={true}
                      showLegend={true}
                      innerRadius={isMobile ? 15 : 20}
                      outerRadius={isMobile ? 50 : 70}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
