import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/admin/tags/[id]
 * Get a specific tag by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Tag schema directly
    const TagSchema = new mongoose.default.Schema({
      name: { type: String, required: true },
      slug: { type: String, required: true, unique: true },
      count: { type: Number, default: 0 }
    }, {
      timestamps: true
    });

    // Get the Tag model
    const Tag = mongoose.default.models.Tag ||
               mongoose.default.model('Tag', TagSchema);

    // Validate tag ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid tag ID' }, { status: 400 });
    }

    // Find tag
    const tag = await Tag.findById(params.id);
    if (!tag) {
      return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
    }

    // Return tag
    return NextResponse.json({
      id: tag._id.toString(),
      name: tag.name,
      slug: tag.slug,
      count: tag.count,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt
    });
  } catch (error) {
    console.error('Error fetching tag:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tag', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/tags/[id]
 * Update a specific tag
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Tag schema directly
    const TagSchema = new mongoose.default.Schema({
      name: { type: String, required: true },
      slug: { type: String, required: true, unique: true },
      count: { type: Number, default: 0 }
    }, {
      timestamps: true
    });

    // Get the Tag model
    const Tag = mongoose.default.models.Tag ||
               mongoose.default.model('Tag', TagSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate tag ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid tag ID' }, { status: 400 });
    }

    // Get request data
    const data = await request.json();

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: 'Tag name is required' }, { status: 400 });
    }

    // Generate slug if not provided
    if (!data.slug) {
      data.slug = data.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    }

    // Check if slug already exists (excluding this tag)
    const existingTag = await Tag.findOne({
      slug: data.slug,
      _id: { $ne: params.id }
    });
    if (existingTag) {
      return NextResponse.json({ error: 'Tag with this slug already exists' }, { status: 400 });
    }

    // Update tag
    const updatedTag = await Tag.findByIdAndUpdate(
      params.id,
      {
        name: data.name,
        slug: data.slug
      },
      { new: true }
    );

    if (!updatedTag) {
      return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
    }

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'update_tag',
      details: `Admin updated tag: ${data.name}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { tagId: params.id }
    });

    // Return updated tag
    return NextResponse.json({
      id: updatedTag._id.toString(),
      name: updatedTag.name,
      slug: updatedTag.slug,
      count: updatedTag.count,
      createdAt: updatedTag.createdAt,
      updatedAt: updatedTag.updatedAt
    });
  } catch (error) {
    console.error('Error updating tag:', error);
    return NextResponse.json(
      { error: 'Failed to update tag', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/tags/[id]
 * Delete a specific tag
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to get the user ID from the cookie directly
    let userId = request.cookies.get('userId')?.value;

    // If not in cookies, try to extract from request headers (Authorization header)
    if (!userId) {
      const authHeader = request.headers.get('Authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        userId = authHeader.substring(7); // Remove 'Bearer ' prefix
      }
    }

    // If still no userId, check query parameters
    if (!userId) {
      const url = new URL(request.url);
      userId = url.searchParams.get('userId') || '';
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      role: String
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as any).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the Tag schema directly
    const TagSchema = new mongoose.default.Schema({
      name: { type: String, required: true },
      slug: { type: String, required: true, unique: true },
      count: { type: Number, default: 0 }
    }, {
      timestamps: true
    });

    // Get the Tag model
    const Tag = mongoose.default.models.Tag ||
               mongoose.default.model('Tag', TagSchema);

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Validate tag ID
    if (!mongoose.default.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid tag ID' }, { status: 400 });
    }

    // Find tag first to get its name for logging
    const tag = await Tag.findById(params.id);
    if (!tag) {
      return NextResponse.json({ error: 'Tag not found' }, { status: 404 });
    }

    // Delete tag
    await Tag.findByIdAndDelete(params.id);

    // Log admin activity directly
    await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type: 'admin',
      action: 'delete_tag',
      details: `Admin deleted tag: ${tag.name}`,
      ipAddress: request.ip || request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date(),
      metadata: { tagId: params.id }
    });

    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting tag:', error);
    return NextResponse.json(
      { error: 'Failed to delete tag', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
