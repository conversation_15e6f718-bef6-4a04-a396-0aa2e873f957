@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@layer base {
  :root {
    /* Base theme colors - dark mode by default, inspired by Apple TV+ */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* StreamVista custom colors - refined for Apple TV+ aesthetic */
    --vista-dark: 0 0% 0%; /* Pure black background like Apple TV+ */
    --vista-dark-lighter: 220 10% 12%; /* Slightly lighter than black for cards and sections */
    --vista-light: 0 0% 100%; /* Pure white text */
    --vista-blue: 210 100% 50%; /* Apple-inspired blue for buttons and highlights */
    --vista-accent: 210 90% 50%; /* Apple-inspired blue */
    --vista-accent-dim: 210 90% 40%; /* Dimmed version of accent */
    --vista-accent-light: 210 90% 60%; /* Lighter version of accent */
    --vista-card: 240 10% 10%; /* Slightly lighter than black for cards */
    --vista-card-hover: 240 10% 15%; /* Hover state for cards */
    --vista-overlay: 240 10% 5% 90%; /* Transparent overlay for modals and menus */
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fix for initial page load issues */
html:not(.app-initialized) .navbar-fixed {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

html.app-initialized .navbar-fixed {
  opacity: 1;
}

/* Ensure sign-in button is always clickable */
button[data-signin-button="true"] {
  position: relative;
  z-index: 100;
}

/* Custom gradient text */
.text-gradient {
  @apply text-vista-accent;
}

/* Custom card hover effect - more subtle like Apple TV+ */
.hover-card-effect {
  @apply transition-transform duration-200;
}

/* Only apply hover effects on desktop */
@media (min-width: 769px) {
  .hover-card-effect {
    @apply hover:scale-[1.02] hover:shadow-lg hover:shadow-black/40;
  }
}

/* Image hover effects similar to Apple TV+ - optimized for mobile */
.content-image-hover {
  @apply transition-transform duration-300;
}

/* Only apply hover effects on desktop */
@media (min-width: 769px) {
  .group:hover .content-image-hover {
    @apply scale-105;
  }
}

/* Pill badges like Apple TV+ */
.badge {
  @apply inline-flex px-2 py-0.5 rounded-full text-xs font-medium;
}
.badge-accent {
  @apply bg-vista-accent/20 text-vista-accent;
}
.badge-light {
  @apply bg-vista-light/20 text-vista-light;
}

/* Custom shadow effects for profiles and UI elements */
.shadow-glow {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.1), 0 0 30px rgba(255, 255, 255, 0.05);
}

.shadow-glow-blue {
  box-shadow: 0 0 15px rgba(0, 123, 255, 0.15), 0 0 30px rgba(0, 123, 255, 0.1);
}

.shadow-glow-amber {
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.15), 0 0 30px rgba(245, 158, 11, 0.1);
}

/* VidSrc Player Styles - Fix for controls visibility */
.native-player-container {
  position: relative;
  user-select: none;
}

.native-player-container iframe {
  width: 100%;
  height: 100%;
  border: 0;
  position: relative;
  z-index: 1;
}

/* Create an interactive area at the bottom of the player that triggers hover state */
.native-player-container::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px; /* Taller control area */
  background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
  opacity: 0;
  z-index: 2;
  pointer-events: none; /* Let events pass through to iframe */
  transition: opacity 0.2s ease;
}

/* When hovering anywhere in the container, make the control area visible */
.native-player-container:hover::before {
  opacity: 0.1; /* Slightly visible gradient to show active area */
}

/* Create a hover trigger area specifically for the bottom controls */
.native-player-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px;
  background: transparent;
  z-index: 3;
  pointer-events: none;
}

/* Force the iframe to maintain its hover state when hovering over the container */
.native-player-container:hover iframe {
  pointer-events: auto !important;
}

/* Target Webkit browsers specifically (Chrome, Safari, Edge) */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  .native-player-container:hover iframe {
    z-index: 2;
  }
}

/* Target Firefox */
@-moz-document url-prefix() {
  .native-player-container iframe {
    z-index: 2;
  }

  .native-player-container::after {
    height: 170px; /* Extra height for Firefox */
  }
}

/* Remove old unused player styles */
.player-container {
  position: relative;
  user-select: none;
}

.player-container iframe {
  width: 100%;
  height: 100%;
  border: 0;
  pointer-events: auto !important;
}

.player-container:hover .player-controls {
  opacity: 1;
  pointer-events: auto;
}

.player-container .player-controls {
  transition: opacity 0.2s ease-in-out;
}

.player-container::after {
  content: none;
}

/* Hero section styles */
.hero-container {
  transition: all 0.3s ease-in-out;
}

.hero-content {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Hero button styles */
.watch-button {
  background-color: hsl(var(--vista-blue)) !important;
  color: white !important;
  border: 1px solid transparent !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2) !important;
}

.watch-button:hover {
  background-color: hsl(var(--vista-blue), 0.9) !important;
  transform: translateY(-1px);
}

.details-button {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-color: hsla(var(--vista-blue), 0.8) !important;
  color: white !important;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2) !important;
}

.details-button:hover {
  background-color: hsla(var(--vista-blue), 0.2) !important;
  transform: translateY(-1px);
}

/* Mobile menu */
@media (max-width: 768px) {
  /* Auth page mobile optimizations */
  .auth-page-mobile {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    display: flex;
    flex-direction: column;
  }

  .auth-form-container {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    min-height: 0;
    max-height: calc(var(--vh, 1vh) * 100 - 110px); /* Adjust for header and footer */
  }

  .auth-header {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .auth-footer {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mobile-menu-open {
    @apply overflow-hidden;
  }

  /* Add padding for content under fixed navbar */
  .hero-section {
    @apply pt-16;
  }

  /* Adjust hero content for better mobile display */
  .hero-content {
    @apply pb-6;
  }

  /* Ensure navbar is visible on mobile */
  .navbar-fixed {
    @apply bg-black bg-opacity-90 backdrop-blur-md;
  }

  /* Mobile-friendly hero buttons */
  .hero-buttons {
    @apply flex-wrap gap-1.5 mt-2;
  }

  .hero-buttons button,
  .hero-buttons a button {
    @apply py-1.5 px-3 text-sm;
  }

  /* Make buttons slightly larger and more prominent on mobile */
  .watch-button, .details-button {
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
  }

  /* Admin Dashboard Mobile Optimizations */
  .admin-mobile-container {
    @apply px-2 sm:px-3 py-3 sm:py-4 space-y-3 sm:space-y-4;
  }

  .admin-card-mobile {
    @apply p-3 sm:p-4 rounded-lg sm:rounded-xl bg-vista-dark/60 border border-vista-light/10;
    min-height: 100px;
    transition: all 0.2s ease-in-out;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .admin-card-mobile:hover {
    @apply bg-vista-dark/70 border-vista-blue/20;
    transform: translateY(-1px);
  }

  .admin-stat-mobile {
    @apply text-2xl font-bold text-vista-light;
    line-height: 1.2;
  }

  .admin-button-mobile {
    @apply h-10 px-4 text-sm font-medium rounded-lg;
    min-width: 44px; /* Minimum touch target */
    touch-action: manipulation;
  }

  .admin-nav-mobile {
    @apply p-4 rounded-lg;
    min-height: 48px; /* Minimum touch target */
    touch-action: manipulation;
  }

  /* Improved scrolling for mobile */
  .admin-scroll-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Better focus states for mobile */
  .admin-focus-mobile:focus {
    @apply outline-none ring-2 ring-vista-blue/50 ring-offset-2 ring-offset-vista-dark;
  }

  /* Enhanced Mobile Typography */
  .admin-title-mobile {
    @apply text-lg font-semibold text-vista-light;
    line-height: 1.3;
  }

  .admin-subtitle-mobile {
    @apply text-sm text-vista-light/70;
    line-height: 1.4;
  }

  .admin-text-mobile {
    @apply text-sm text-vista-light;
    line-height: 1.5;
  }

  .admin-caption-mobile {
    @apply text-xs text-vista-light/60;
    line-height: 1.4;
  }

  /* Mobile-optimized spacing */
  .admin-spacing-mobile {
    @apply space-y-3;
  }

  .admin-spacing-tight-mobile {
    @apply space-y-2;
  }

  .admin-spacing-loose-mobile {
    @apply space-y-4;
  }

  /* Mobile table optimizations */
  .admin-table-mobile {
    font-size: 14px;
  }

  .admin-table-mobile th,
  .admin-table-mobile td {
    @apply px-2 py-3;
  }

  .admin-table-mobile th {
    @apply text-xs font-medium text-vista-light/70 uppercase tracking-wider;
  }

  /* Mobile chart optimizations */
  .admin-chart-mobile {
    @apply rounded-lg overflow-hidden;
  }

  .admin-chart-mobile .recharts-tooltip-wrapper {
    @apply z-50;
  }

  /* Mobile card header optimizations */
  .admin-card-header-mobile {
    @apply px-4 py-3 border-b border-vista-light/10;
  }

  .admin-card-content-mobile {
    @apply px-4 py-3;
  }

  .admin-card-footer-mobile {
    @apply px-4 py-3 border-t border-vista-light/10;
  }

  /* Hide scrollbar for mobile tabs */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Extra small device optimizations */
@media (max-width: 374px) {
  /* Ensure navbar elements fit on very small screens */
  .navbar-fixed .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Reduce logo size on very small screens */
  .navbar-fixed .logo-container {
    transform: scale(0.9);
    transform-origin: left center;
  }

  /* Ensure hamburger menu is always visible */
  .navbar-fixed .mobile-menu-button {
    min-width: 36px !important;
    padding: 0 !important;
    margin-left: 0.25rem;
  }
}

/* Enhanced button hover effects for desktop */
@media (min-width: 769px) {
  .watch-button, .details-button {
    transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  }

  .watch-button:hover, .details-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.3) !important;
  }
}

@layer utilities {
  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-track-vista-dark-lighter::-webkit-scrollbar-track {
    background-color: rgb(12, 15, 22);
  }

  .scrollbar-thumb-vista-light\/20::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.2);
    border-radius: 9999px;
  }

  .hover\:scrollbar-thumb-vista-light\/30:hover::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.3);
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(229, 231, 235, 0.2);
    border-radius: 20px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(229, 231, 235, 0.3);
  }

  /* Enhanced scrollbar for chat and other components that need more visible scrolling */
  .enhanced-scrollbar::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .enhanced-scrollbar::-webkit-scrollbar-track {
    background: rgba(12, 15, 22, 0.6);
    border-radius: 10px;
  }

  .enhanced-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    border: 2px solid rgba(12, 15, 22, 0.6);
  }

  .enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 130, 246, 0.5);
  }

  .enhanced-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) rgba(12, 15, 22, 0.6);
  }
}

/* Custom Toaster Styles */
.vista-toaster [data-close-button] {
  color: rgba(255, 255, 255, 0.6) !important;
  transition: color 0.2s ease;
}

.vista-toaster [data-close-button]:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Admin Dashboard Enhancements */
.shadow-glow-sm {
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

.shadow-glow-md {
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
}

/* Clean, modern scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  border: none;
  min-height: 40px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Firefox scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) rgba(0, 0, 0, 0.2);
}

/* Animation utilities */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 16px rgba(59, 130, 246, 0.8);
  }
}

/* Only apply pulse animations on desktop to prevent mobile performance issues */
@media (min-width: 769px) {
  .pulse-glow {
    animation: pulse-glow 2s infinite ease-in-out;
  }
}

/* Simplified version for mobile */
@media (max-width: 768px) {
  .pulse-glow {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
  }
}

/* Gradient backgrounds */
.bg-gradient-vista {
  background: linear-gradient(135deg, #101828 0%, #1e293b 100%);
}

.bg-gradient-vista-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Card hover effects - optimized for performance */
.card-hover-effect {
  transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
}

/* Chart animations */
@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes growUp {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

@keyframes growRight {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

.chart-animate-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.chart-bar {
  transform-origin: bottom;
  animation: growUp 0.5s ease-out forwards;
}

.chart-bar-horizontal {
  transform-origin: left;
  animation: growRight 0.5s ease-out forwards;
}

/* Only apply hover effects on desktop */
@media (min-width: 769px) {
  .card-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

/* Watch Party Glass Card Effect - optimized for performance */
.wp-glass-card {
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.3);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Apply backdrop-filter only on desktop for better performance */
@media (min-width: 769px) {
  .wp-glass-card {
    backdrop-filter: blur(12px);
  }

  .wp-glass-card:hover {
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.4);
  }
}

/* Watch Party Scrollbar */
.wp-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.wp-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.wp-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 8px;
}

.wp-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.wp-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) rgba(0, 0, 0, 0.2);
}

/* Navigation transition overlay - optimized for mobile */
body.navigation-in-progress::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  animation: fadeIn 0.15s ease-in-out;
  pointer-events: none; /* Allow clicks to pass through */
}

/* Only apply blur effect on desktop for better performance */
@media (min-width: 769px) {
  body.navigation-in-progress::before {
    backdrop-filter: blur(2px);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Blob animation for StreamingFeatures component */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Only apply blob animations on desktop to prevent mobile performance issues */
@media (min-width: 769px) {
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }
}

/* Custom container for enforcing scrollbars */
.custom-scrollbar-container {
  position: relative;
}

/* Outer container to ensure scrollbar visibility */
.scrollbar-outer-container {
  position: relative;
}

/* Custom scrollbar for admin panels */
.admin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--vista-light), 0.2) rgba(var(--vista-dark-lighter), 0.3);
}

.admin-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.admin-scrollbar::-webkit-scrollbar-track {
  background: rgba(var(--vista-dark-lighter), 0.3);
  border-radius: 8px;
}

.admin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vista-light), 0.2);
  border-radius: 8px;
  border: 2px solid rgba(var(--vista-dark-lighter), 0.3);
  transition: all 0.2s ease;
}

.admin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--vista-light), 0.3);
}

.admin-scrollbar::-webkit-scrollbar-corner {
  background-color: transparent;
}
