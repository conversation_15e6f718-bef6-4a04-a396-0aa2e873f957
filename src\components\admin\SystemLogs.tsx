'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  RefreshCw,
  Info,
  Bug,
  Server,
  Shield,
  Cpu,
  Database,
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToastHelpers } from '@/lib/ToastContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';

interface SystemLog {
  _id: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  details?: string;
  timestamp: string;
}

interface SystemLogsProps {
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  height?: string;
}

export default function SystemLogs({
  limit = 20,
  autoRefresh: initialAutoRefresh = false,
  refreshInterval: initialRefreshInterval = 60000,
  height = "500px"
}: SystemLogsProps) {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logLevel, setLogLevel] = useState<string>('all');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(initialAutoRefresh);
  const [refreshInterval, setRefreshInterval] = useState(initialRefreshInterval);

  const { user, isAdmin } = useAuth();
  const toast = useToastHelpers();
  const retryCount = useRef(0);
  const maxRetries = 2;
  const toastShown = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFetching = useRef(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch system logs with debouncing and spam prevention
  const fetchLogs = useCallback(async (silent = false) => {
    // Prevent multiple simultaneous requests
    if (isFetching.current) {
      return;
    }

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      isFetching.current = true;

      if (!silent) {
        setIsLoading(true);
      }
      setError(null);

    try {
      if (!user || !user.id) {
        throw new Error('Authentication required. Please sign in again.');
      }

      if (!isAdmin()) {
        throw new Error('You do not have permission to view system logs.');
      }

      const url = new URL('/api/admin/system/logs', window.location.origin);
      url.searchParams.append('limit', limit.toString());
      url.searchParams.append('userId', user.id);

      if (logLevel !== 'all') {
        url.searchParams.append('level', logLevel);
      }

      if (!autoRefresh) {
        url.searchParams.append('_t', Date.now().toString());
      }

      const response = await fetch(url.toString(), {
        credentials: 'include',
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in again.');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to view system logs.');
        }
        throw new Error(errorData.error || `Failed to fetch logs (${response.status})`);
      }

      const data = await response.json();
      setLogs(data.logs || []);
      setLastRefresh(new Date());
      retryCount.current = 0;
      toastShown.current = false;
    } catch (error) {
      console.error('Error fetching system logs:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);

      if (!toastShown.current && !silent && retryCount.current >= maxRetries) {
        toast.error('Error', 'Failed to fetch system logs');
        toastShown.current = true;
      }

      if (retryCount.current < maxRetries) {
        const delay = Math.pow(2, retryCount.current) * 1000;
        retryCount.current++;
        fetchTimeoutRef.current = setTimeout(() => {
          fetchLogs(true);
        }, delay);
      }
    } finally {
      setIsLoading(false);
      isFetching.current = false;
    }
    }, 300); // 300ms debounce delay
  }, [limit, logLevel, toast, autoRefresh, user, isAdmin]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefreshTimeoutRef.current) {
      clearTimeout(autoRefreshTimeoutRef.current);
      autoRefreshTimeoutRef.current = null;
    }

    if (autoRefresh && refreshInterval > 0) {
      autoRefreshTimeoutRef.current = setTimeout(() => {
        fetchLogs(true);
      }, refreshInterval);
    }

    return () => {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
    };
  }, [autoRefresh, refreshInterval]);

  // Initial fetch - only run once on mount
  useEffect(() => {
    fetchLogs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle filter changes
  useEffect(() => {
    if (logs.length > 0) { // Only refetch if we already have data
      fetchLogs();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [logLevel, limit]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      isFetching.current = false;
    };
  }, []);

  // Helper functions
  const formatRelativeTime = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'debug':
        return <Bug className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source.toLowerCase()) {
      case 'server':
        return <Server className="h-4 w-4" />;
      case 'auth':
        return <Shield className="h-4 w-4" />;
      case 'system':
        return <Cpu className="h-4 w-4" />;
      case 'database':
        return <Database className="h-4 w-4" />;
      default:
        return <Server className="h-4 w-4" />;
    }
  };

  const getLogLevelVariant = (level: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (level) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'secondary';
      case 'debug':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const displayLogs = logs.length > 0 ? logs : [];

  return (
    <Card>
      <CardHeader className="px-4 sm:px-6 py-4 sm:py-6">
        <div className="flex flex-col gap-4 sm:gap-0 sm:flex-row sm:items-center sm:justify-between">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-lg sm:text-xl text-vista-light">System Logs</CardTitle>
            <CardDescription className="text-sm text-vista-light/70 mt-1">
              Server and system-level events
            </CardDescription>
          </div>
          <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row sm:items-center sm:space-x-4">
            {/* Auto-refresh toggle - Mobile optimized */}
            <div className="flex items-center justify-between sm:justify-start space-x-2">
              <Label htmlFor="auto-refresh" className="text-sm text-vista-light/70 flex-1 sm:flex-none">Auto-refresh</Label>
              <Switch
                id="auto-refresh"
                checked={autoRefresh}
                onCheckedChange={setAutoRefresh}
                className="flex-shrink-0"
              />
            </div>

            {/* Controls row - Mobile optimized */}
            <div className="flex items-center gap-2 sm:gap-3">
              <Select value={logLevel} onValueChange={setLogLevel}>
                <SelectTrigger className="w-24 sm:w-32 h-9 sm:h-10 text-xs sm:text-sm">
                  <SelectValue placeholder="Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="debug">Debug</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchLogs()}
                disabled={isLoading}
                className="h-9 sm:h-10 px-3 sm:px-4 text-xs sm:text-sm transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95 touch-manipulation min-w-[44px]"
              >
                <RefreshCw className={`mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">Refresh</span>
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {error && !isLoading ? (
          <div className="p-4 sm:p-6 text-center">
            <AlertCircle className="h-6 w-6 sm:h-8 sm:w-8 text-red-400 mx-auto mb-2" />
            <p className="text-sm sm:text-base text-red-400 mb-4 break-words">{error}</p>
            <Button
              variant="outline"
              onClick={() => fetchLogs()}
              className="h-9 sm:h-10 px-4 text-sm transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95 touch-manipulation min-w-[44px]"
            >
              <RefreshCw className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              Try Again
            </Button>
          </div>
        ) : (
          <div className="relative border rounded-md overflow-hidden">
            {/* Mobile-optimized layout */}
            <div className="block md:hidden">
              <div
                className="admin-scroll-mobile overflow-auto"
                style={{ height, overflowX: "hidden" }}
              >
                <div className="admin-mobile-container">
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="admin-card-mobile">
                        <Skeleton className="h-4 w-20 mb-2" />
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    ))
                  ) : displayLogs.length > 0 ? (
                    displayLogs.map((log) => (
                      <div
                        key={log._id}
                        className={`admin-card-mobile ${log.level === 'error' ? 'border-red-500/30 bg-red-900/20' :
                          log.level === 'warning' ? 'border-yellow-500/30 bg-yellow-900/20' : ''}`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <Badge
                            variant={getLogLevelVariant(log.level)}
                            className="flex items-center gap-1.5 text-xs px-2.5 py-1.5 font-medium"
                          >
                            {getLogIcon(log.level)}
                            <span className="capitalize">{log.level}</span>
                          </Badge>
                          <span className="text-xs text-vista-light/60 font-mono whitespace-nowrap ml-2">
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2.5 mb-3 p-2 rounded-lg bg-vista-dark/30 border border-vista-light/5">
                          <div className="flex-shrink-0 text-vista-blue">
                            {getSourceIcon(log.source)}
                          </div>
                          <span className="text-sm font-medium text-vista-light capitalize">{log.source}</span>
                        </div>
                        <div className="space-y-3">
                          <p className="text-sm text-vista-light/90 break-words leading-relaxed">{log.message}</p>
                          {log.details && (
                            <details className="group">
                              <summary className="text-xs text-vista-blue cursor-pointer hover:text-vista-blue/80 select-none py-2 px-3 rounded-md bg-vista-dark/40 border border-vista-light/10 transition-colors group-open:bg-vista-blue/10 group-open:border-vista-blue/20 touch-manipulation min-h-[44px] flex items-center">
                                <span>View Details</span>
                              </summary>
                              <div className="mt-2 p-3 rounded-lg bg-vista-dark/50 border border-vista-light/10">
                                <pre className="text-xs text-vista-light/70 whitespace-pre-wrap break-words leading-relaxed">
                                  {log.details}
                                </pre>
                              </div>
                            </details>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 sm:py-12 text-vista-light/50 px-4">
                      <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-3 opacity-50" />
                      <p className="text-sm sm:text-base">No logs available</p>
                      <p className="text-xs sm:text-sm text-vista-light/40 mt-2">
                        System logs will appear here when events occur
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Desktop table layout */}
            <div className="hidden md:block">
              <div
                className="custom-scrollbar overflow-auto"
                style={{ height, overflowX: "auto" }}
              >
                <table className="w-full border-collapse min-w-[600px]">
                  <thead className="sticky top-0 bg-vista-dark/90 backdrop-blur-sm z-10">
                    <tr>
                      <th className="w-32 lg:w-36 text-vista-light/70 text-left p-2 lg:p-3 border-b border-vista-light/10 text-xs lg:text-sm font-medium">Time</th>
                      <th className="w-20 lg:w-24 text-vista-light/70 text-left p-2 lg:p-3 border-b border-vista-light/10 text-xs lg:text-sm font-medium">Level</th>
                      <th className="w-24 lg:w-32 text-vista-light/70 text-left p-2 lg:p-3 border-b border-vista-light/10 text-xs lg:text-sm font-medium">Source</th>
                      <th className="text-vista-light/70 text-left p-2 lg:p-3 border-b border-vista-light/10 text-xs lg:text-sm font-medium">Message</th>
                      <th className="w-16 lg:w-20 text-vista-light/70 text-center p-2 lg:p-3 border-b border-vista-light/10 text-xs lg:text-sm font-medium">Details</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      Array.from({ length: 5 }).map((_, index) => (
                        <tr key={index}>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-24" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-6 w-16" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-20" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-full" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-12" />
                          </td>
                        </tr>
                      ))
                    ) : displayLogs.length > 0 ? (
                      displayLogs.map((log) => (
                        <tr key={log._id} className={`hover:bg-vista-light/5 transition-colors duration-150 ${
                          log.level === 'error' ? 'bg-red-900/10' :
                          log.level === 'warning' ? 'bg-yellow-900/10' : ''
                        }`}>
                          <td className="p-2 lg:p-3 border-b border-vista-light/5 text-xs lg:text-sm text-vista-light/70 font-mono">
                            <div className="truncate" title={new Date(log.timestamp).toLocaleString()}>
                              {formatRelativeTime(log.timestamp)}
                            </div>
                          </td>
                          <td className="p-2 lg:p-3 border-b border-vista-light/5">
                            <Badge
                              variant={getLogLevelVariant(log.level)}
                              className="flex items-center gap-1 w-fit text-xs px-2 py-1"
                            >
                              {getLogIcon(log.level)}
                              <span className="capitalize">{log.level}</span>
                            </Badge>
                          </td>
                          <td className="p-2 lg:p-3 border-b border-vista-light/5">
                            <div className="flex items-center gap-1.5">
                              <div className="text-vista-blue flex-shrink-0">
                                {getSourceIcon(log.source)}
                              </div>
                              <span className="text-xs lg:text-sm text-vista-light capitalize truncate">{log.source}</span>
                            </div>
                          </td>
                          <td className="p-2 lg:p-3 border-b border-vista-light/5 text-xs lg:text-sm text-vista-light/90 max-w-xs lg:max-w-md">
                            <div className="truncate" title={log.message}>
                              {log.message}
                            </div>
                          </td>
                          <td className="p-2 lg:p-3 border-b border-vista-light/5 text-center">
                            {log.details && (
                              <details className="inline group">
                                <summary className="text-xs text-vista-blue cursor-pointer hover:text-vista-blue/80 px-2 py-1 rounded transition-colors group-open:bg-vista-blue/10 touch-manipulation min-h-[32px] flex items-center justify-center">
                                  View
                                </summary>
                                <div className="absolute z-50 mt-1 p-3 bg-vista-dark border border-vista-light/20 rounded-lg shadow-xl max-w-xs lg:max-w-sm right-0 lg:left-0">
                                  <pre className="text-xs text-vista-light/70 whitespace-pre-wrap break-words leading-relaxed">
                                    {log.details}
                                  </pre>
                                </div>
                              </details>
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="text-center py-8 lg:py-12 text-vista-light/50 px-4">
                          <AlertCircle className="h-6 w-6 lg:h-8 lg:w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm lg:text-base">No logs available</p>
                          <p className="text-xs lg:text-sm text-vista-light/40 mt-2">
                            System logs will appear here when events occur
                          </p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        <div className="text-xs text-vista-light/50 mt-2 text-center sm:text-right p-3 sm:p-4 border-t border-vista-light/5">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-1 sm:gap-0">
            <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
            {autoRefresh && (
              <span className="sm:ml-2">
                (Auto-refresh {Math.round(refreshInterval / 1000)}s)
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
