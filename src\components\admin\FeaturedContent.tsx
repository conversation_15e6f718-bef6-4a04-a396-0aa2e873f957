'use client';

import { useState, useEffect } from 'react';
import { useFeaturedContent } from '@/hooks/useFeaturedContent';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  Star,
  StarOff,
  ArrowUp,
  ArrowDown,
  Film,
  Tv,
  Trash2,
  Plus,
  Search,
  Calendar
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import { DatePicker } from '@/components/ui/date-picker';
import ConfirmationDialog, { useConfirmationDialog } from './ConfirmationDialog';

interface ContentItem {
  id: string;
  contentId: string;
  title: string;
  type: 'movie' | 'tv';
  posterPath?: string;
  backdropPath?: string;
  year?: string;
  genres?: string[];
  order: number;
  startDate?: string;
  endDate?: string;
}

interface AvailableContent {
  id: string;
  title: string;
  type: 'movie' | 'tv';
  posterPath?: string;
  year?: string;
  featured?: boolean;
  order?: number;
}

interface MovieApiResponse {
  id: string;
  title: string;
  posterPath?: string;
  year?: string;
}

interface ShowApiResponse {
  id: string;
  title: string;
  posterPath?: string;
  year?: string;
}

export default function FeaturedContent() {
  // Auth context
  const { user } = useAuth();

  // Use the featured content hook
  const {
    featuredContent,
    isLoading,
    error,
    refetch,
    addFeaturedContent,
    updateFeaturedContent,
    removeFeaturedContent,
    reorderFeaturedContent
  } = useFeaturedContent();
  
  const { openDialog, ConfirmationDialog } = useConfirmationDialog();

  // State for content selection modal
  const [isSelectionModalOpen, setIsSelectionModalOpen] = useState(false);
  const [availableContent, setAvailableContent] = useState<AvailableContent[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredContent, setFilteredContent] = useState<AvailableContent[]>([]);
  const [selectedContent, setSelectedContent] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for scheduling modal
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);
  const [scheduledItem, setScheduledItem] = useState<ContentItem | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  // Load featured content on mount
  useEffect(() => {
    if (!featuredContent) {
      refetch();
    }
  }, [featuredContent, refetch]);

  // Fetch real content from the API
  const fetchAvailableContent = async () => {
    try {
      setIsSubmitting(true);

      // Fetch popular movies and shows from the API
      const [moviesResponse, showsResponse] = await Promise.all([
        fetch('/api/content/popular-movies?page=1'),
        fetch('/api/content/popular-shows?page=1')
      ]);

      if (!moviesResponse.ok || !showsResponse.ok) {
        throw new Error('Failed to fetch content');
      }

      const moviesData = await moviesResponse.json();
      const showsData = await showsResponse.json();

      // Combine and format the data
      const availableContent: AvailableContent[] = [
        ...moviesData.data.map((movie: MovieApiResponse) => ({
          id: movie.id,
          title: movie.title,
          type: 'movie' as const,
          posterPath: movie.posterPath,
          year: movie.year,
          featured: false
        })),
        ...showsData.data.map((show: ShowApiResponse) => ({
          id: show.id,
          title: show.title,
          type: 'tv' as const,
          posterPath: show.posterPath,
          year: show.year,
          featured: false
        }))
      ];

      setAvailableContent(availableContent);
      setFilteredContent(availableContent);
    } catch (error) {
      console.error('Error fetching available content:', error);
      toast({
        title: 'Error',
        description: 'Failed to load available content',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter available content based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredContent(availableContent);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredContent(
        availableContent.filter(item =>
          item.title.toLowerCase().includes(query)
        )
      );
    }
  }, [searchQuery, availableContent]);

  // Handle opening selection modal
  const handleAddFeatured = async () => {
    setSelectedContent([]);
    setSearchQuery('');
    setIsSelectionModalOpen(true);
    await fetchAvailableContent();
  };

  // Handle content selection
  const handleContentSelection = (contentId: string) => {
    setSelectedContent(prev => {
      if (prev.includes(contentId)) {
        return prev.filter(id => id !== contentId);
      } else {
        return [...prev, contentId];
      }
    });
  };

  // Handle adding selected content to featured
  const handleAddSelected = async () => {
    if (selectedContent.length === 0) {
      toast({
        title: 'No Content Selected',
        description: 'Please select at least one item to feature',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Add each selected content to featured
      for (const contentId of selectedContent) {
        await addFeaturedContent(contentId);
      }

      // Close modal
      setIsSelectionModalOpen(false);

      toast({
        title: 'Content Featured',
        description: `Added ${selectedContent.length} item(s) to featured content`,
        variant: 'success'
      });
    } catch (error) {
      console.error('Error adding featured content:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add featured content',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle removing content from featured
  const handleRemoveFeatured = (id: string) => {
    openDialog({
      title: 'Remove from Featured',
      description: 'Are you sure you want to remove this item from featured content? It will no longer appear on the homepage.',
      confirmText: 'Remove from Featured',
      variant: 'default',
      onConfirm: () => performRemoveFeatured(id)
    });
  };

  const performRemoveFeatured = async (id: string) => {

    try {
      await removeFeaturedContent(id);

      toast({
        title: 'Content Removed',
        description: 'Item removed from featured content',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error removing featured content:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove featured content',
        variant: 'destructive'
      });
    }
  };

  // Handle moving content up in order
  const handleMoveUp = async (index: number) => {
    if (!featuredContent || index === 0) return;

    try {
      const items = [...featuredContent];
      const temp = items[index];

      // Swap positions
      items[index] = items[index - 1];
      items[index - 1] = temp;

      // Update order properties
      items.forEach((item, i) => {
        item.order = i + 1;
      });

      // Save new order
      await reorderFeaturedContent(items.map(item => ({ id: item.id })));
    } catch (error) {
      console.error('Error reordering featured content:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reorder featured content',
        variant: 'destructive'
      });
    }
  };

  // Handle moving content down in order
  const handleMoveDown = async (index: number) => {
    if (!featuredContent || index === featuredContent.length - 1) return;

    try {
      const items = [...featuredContent];
      const temp = items[index];

      // Swap positions
      items[index] = items[index + 1];
      items[index + 1] = temp;

      // Update order properties
      items.forEach((item, i) => {
        item.order = i + 1;
      });

      // Save new order
      await reorderFeaturedContent(items.map(item => ({ id: item.id })));
    } catch (error) {
      console.error('Error reordering featured content:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reorder featured content',
        variant: 'destructive'
      });
    }
  };

  // Handle opening schedule modal
  const handleOpenScheduleModal = (item: ContentItem) => {
    setScheduledItem(item);
    setStartDate(item.startDate ? new Date(item.startDate) : null);
    setEndDate(item.endDate ? new Date(item.endDate) : null);
    setIsScheduleModalOpen(true);
  };

  // Handle saving schedule
  const handleSaveSchedule = async () => {
    if (!scheduledItem) return;

    try {
      await updateFeaturedContent(
        scheduledItem.id,
        scheduledItem.order,
        startDate || undefined,
        endDate || undefined
      );

      setIsScheduleModalOpen(false);

      toast({
        title: 'Schedule Updated',
        description: 'Featured content schedule has been updated',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error updating schedule:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update schedule',
        variant: 'destructive'
      });
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-4 sm:space-y-6 max-w-full overflow-hidden">
      <Card className="overflow-hidden">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4">
          <div className="min-w-0">
            <CardTitle className="text-vista-light text-lg sm:text-xl">Featured Content</CardTitle>
            <CardDescription className="text-sm">
              Manage content featured on the homepage
            </CardDescription>
          </div>
          <Button onClick={handleAddFeatured} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            <span className="text-sm sm:text-base">Add Featured Content</span>
          </Button>
        </CardHeader>
        <CardContent className="px-3 sm:px-6">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-vista-light/50" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p className="text-sm sm:text-base">{error instanceof Error ? error.message : String(error)}</p>
              <Button
                variant="outline"
                onClick={refetch}
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block rounded-md border border-vista-light/10 overflow-hidden">
                <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order</TableHead>
                    <TableHead>Poster</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {featuredContent && featuredContent.length > 0 ? (
                    featuredContent
                      .sort((a: ContentItem, b: ContentItem) => a.order - b.order)
                      .map((content: ContentItem, index: number) => (
                        <TableRow key={content.id}>
                          <TableCell className="font-medium">{content.order}</TableCell>
                          <TableCell>
                            <div className="relative h-16 w-12 overflow-hidden rounded-sm">
                              {content.posterPath ? (
                                <Image
                                  src={content.posterPath}
                                  alt={content.title}
                                  fill
                                  className="object-cover"
                                  onError={(e) => {
                                    e.currentTarget.src = 'https://via.placeholder.com/92x138?text=No+Image';
                                  }}
                                />
                              ) : (
                                <div className="h-full w-full bg-vista-dark-lighter flex items-center justify-center">
                                  {content.type === 'movie' ? (
                                    <Film className="h-6 w-6 text-vista-light/50" />
                                  ) : (
                                    <Tv className="h-6 w-6 text-vista-light/50" />
                                  )}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium text-vista-light">{content.title}</div>
                            <div className="text-vista-light/70 text-sm">{content.year}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {content.type === 'movie' ? (
                                <Film className="mr-1 h-3 w-3" />
                              ) : (
                                <Tv className="mr-1 h-3 w-3" />
                              )}
                              {content.type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1 text-vista-light/70" />
                                <span className="text-vista-light/70">Start:</span>
                                <span className="ml-1">{formatDate(content.startDate)}</span>
                              </div>
                              <div className="flex items-center mt-1">
                                <Calendar className="h-3 w-3 mr-1 text-vista-light/70" />
                                <span className="text-vista-light/70">End:</span>
                                <span className="ml-1">{formatDate(content.endDate)}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleMoveUp(index)}
                                disabled={index === 0}
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleMoveDown(index)}
                                disabled={index === (featuredContent?.length || 0) - 1}
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleOpenScheduleModal(content)}
                              >
                                <Calendar className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveFeatured(content.id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-vista-light/70">
                        No featured content found. Click "Add Featured Content" to add some.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden space-y-3">
              {featuredContent && featuredContent.length > 0 ? (
                featuredContent
                  .sort((a: ContentItem, b: ContentItem) => a.order - b.order)
                  .map((content: ContentItem, index: number) => (
                    <div
                      key={content.id}
                      className="bg-vista-dark/50 border border-vista-light/10 rounded-lg p-3 sm:p-4 hover:bg-vista-dark/70 transition-colors"
                    >
                      <div className="flex gap-3">
                        {/* Poster */}
                        <div className="relative h-20 w-14 flex-shrink-0 overflow-hidden rounded-sm bg-vista-dark-lighter">
                          {content.posterPath ? (
                            <Image
                              src={content.posterPath}
                              alt={content.title}
                              fill
                              className="object-cover"
                              onError={(e) => {
                                e.currentTarget.src = 'https://via.placeholder.com/92x138?text=No+Image';
                              }}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full">
                              <Film className="h-6 w-6 text-vista-light/30" />
                            </div>
                          )}
                        </div>

                        {/* Content Info */}
                        <div className="flex-1 min-w-0 space-y-2">
                          <div className="flex items-start justify-between gap-2">
                            <div className="min-w-0">
                              <h3 className="font-semibold text-vista-light text-sm sm:text-base line-clamp-1">
                                {content.title}
                              </h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {content.type}
                                </Badge>
                                <span className="text-xs text-vista-light/60">
                                  Order: {content.order}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Schedule Info */}
                          <div className="text-xs text-vista-light/70">
                            <div>Start: {formatDate(content.startDate)}</div>
                            <div>End: {formatDate(content.endDate)}</div>
                          </div>

                          {/* Actions */}
                          <div className="flex gap-1 pt-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleMoveUp(index)}
                              disabled={index === 0}
                              className="h-8 px-2"
                            >
                              <ArrowUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleMoveDown(index)}
                              disabled={index === featuredContent.length - 1}
                              className="h-8 px-2"
                            >
                              <ArrowDown className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveFeatured(content.id)}
                              className="h-8 px-2 text-red-500 hover:text-red-700"
                            >
                              <StarOff className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
              ) : (
                <div className="text-center py-8 text-vista-light/70">
                  <Star className="h-12 w-12 mx-auto mb-4 text-vista-light/30" />
                  <p className="text-sm sm:text-base">No featured content found.</p>
                  <p className="text-xs sm:text-sm mt-1">Click "Add Featured Content" to add some.</p>
                </div>
              )}
            </div>
            </>
          )}
        </CardContent>
        <CardFooter className="border-t border-vista-light/10 pt-4 text-sm text-vista-light/70">
          <p>Featured content will be displayed on the homepage in the order shown above.</p>
        </CardFooter>
      </Card>

      {/* Content Selection Modal */}
      <Dialog open={isSelectionModalOpen} onOpenChange={setIsSelectionModalOpen}>
        <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg sm:text-xl">Add Featured Content</DialogTitle>
            <DialogDescription className="text-sm">
              Select content to feature on the homepage
            </DialogDescription>
          </DialogHeader>

          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/50 h-4 w-4" />
            <Input
              placeholder="Search content..."
              className="pl-10 text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4 mt-4 max-h-[50vh] sm:max-h-[60vh] overflow-y-auto">
            {filteredContent.map((content) => (
              <div
                key={content.id}
                className={`
                  relative cursor-pointer rounded-md overflow-hidden border-2 transition-all
                  ${selectedContent.includes(content.id)
                    ? 'border-vista-blue'
                    : 'border-transparent hover:border-vista-light/20'}
                `}
                onClick={() => handleContentSelection(content.id)}
              >
                <div className="relative aspect-[2/3] bg-vista-dark-lighter">
                  {content.posterPath ? (
                    <Image
                      src={content.posterPath}
                      alt={content.title}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        e.currentTarget.src = 'https://via.placeholder.com/300x450?text=No+Image';
                      }}
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center">
                      {content.type === 'movie' ? (
                        <Film className="h-12 w-12 text-vista-light/30" />
                      ) : (
                        <Tv className="h-12 w-12 text-vista-light/30" />
                      )}
                    </div>
                  )}

                  {/* Selection indicator */}
                  {selectedContent.includes(content.id) && (
                    <div className="absolute top-2 right-2 bg-vista-blue rounded-full p-1">
                      <Star className="h-4 w-4 text-white" />
                    </div>
                  )}

                  <div className="p-2">
                    <h4 className="text-sm font-medium text-vista-light truncate">{content.title}</h4>
                    <div className="flex items-center justify-between mt-1">
                      <Badge variant="outline" className="text-xs capitalize">
                        {content.type}
                      </Badge>
                      <span className="text-xs text-vista-light/70">{content.year}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {filteredContent.length === 0 && (
              <div className="col-span-full text-center py-8 text-vista-light/70">
                No content found matching your search.
              </div>
            )}
          </div>

          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsSelectionModalOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddSelected}
              disabled={selectedContent.length === 0 || isSubmitting}
              className="w-full sm:w-auto"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <span className="text-sm">Add Selected ({selectedContent.length})</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Schedule Modal */}
      <Dialog open={isScheduleModalOpen} onOpenChange={setIsScheduleModalOpen}>
        <DialogContent className="w-[95vw] max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg sm:text-xl">Schedule Featured Content</DialogTitle>
            <DialogDescription className="text-sm">
              Set when this content should appear in the featured section
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-vista-light">
                {scheduledItem?.title}
              </h4>
              <p className="text-sm text-vista-light/70">
                Set a date range for when this content should be featured. Leave empty for no restrictions.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-vista-light">Start Date</label>
                <DatePicker
                  date={startDate}
                  onSelect={(date) => setStartDate(date || null)}
                  placeholder="No start date"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-vista-light">End Date</label>
                <DatePicker
                  date={endDate}
                  onSelect={(date) => setEndDate(date || null)}
                  placeholder="No end date"
                />
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsScheduleModalOpen(false)}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveSchedule}
              className="w-full sm:w-auto"
            >
              <span className="text-sm">Save Schedule</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </div>
  );
}
