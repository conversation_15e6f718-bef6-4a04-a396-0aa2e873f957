'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, BarChart3, Trash2, AlertTriangle, RefreshCw } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { useToastHelpers } from '@/lib/ToastContext';
import { Skeleton } from '@/components/ui/skeleton';

interface UserActivityManagementProps {
  onComplete?: () => void;
  onError?: (message: string) => void;
  onLoadingChange?: (loading: boolean) => void;
}

export default function UserActivityManagement({ onComplete, onError, onLoadingChange }: UserActivityManagementProps) {
  const [activeTab, setActiveTab] = useState('purge');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [purgeStats, setPurgeStats] = useState<null | {
    olderThan: number;
    affectedLogs: number;
  }>(null);
  const [olderThan, setOlderThan] = useState(30); // Default: 30 days
  const [userIdToClear, setUserIdToClear] = useState('');
  const [activityType, setActivityType] = useState('');
  // Define proper types for activity stats
  interface ActivityStatsItem {
    _id: string;
    count: number;
  }

  interface ActivityStats {
    byType: ActivityStatsItem[];
    byAction: ActivityStatsItem[];
    timeDistribution: Array<{ date: string; count: number }>;
    totalCount: number;
    oldestLog: string | null;
  }

  const [activityStats, setActivityStats] = useState<ActivityStats | null>(null);
  const [estimatedLogsToDelete, setEstimatedLogsToDelete] = useState<number | null>(null);
  const toast = useToastHelpers();

  // Function to manage activity logs
  // Define proper types for the parameters
  type ActivityAction = 'purge' | 'clearUser' | 'clearType' | 'getStats';
  type ActivityParams = {
    days?: number;
    userId?: string;
    type?: string;
  };

  // Track the last toast time to prevent spam
  const lastToastTimeRef = React.useRef<{ [key: string]: number }>({});

  // Track the last API call time to prevent API spam
  const lastApiCallTimeRef = React.useRef<{ [key: string]: number }>({});

  // Track API responses for caching
  const apiCacheRef = React.useRef<{ [key: string]: { data: { stats: ActivityStats; message?: string }; timestamp: number } }>({});

  // Minimum time between toasts (in milliseconds)
  const MIN_TOAST_INTERVAL = 3000; // 3 seconds

  // Minimum time between API calls (in milliseconds)
  const MIN_API_CALL_INTERVAL = 5000; // Reduced from 10 seconds to 5 seconds for better UX

  // Cache expiration time (in milliseconds)
  const CACHE_EXPIRATION = 300000; // 5 minutes (increased from 60 seconds)

  // When an error occurs, also pass it to the parent component if the callback exists
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    if (onError) {
      onError(errorMessage);
    }
  }, [onError]);

  // Update isLoading state and notify parent component
  const updateLoadingState = useCallback((loading: boolean) => {
    setIsLoading(loading);
    if (onLoadingChange) {
      onLoadingChange(loading);
    }
  }, [onLoadingChange]);

  const manageActivities = useCallback(async (action: ActivityAction, params: ActivityParams = {}, showToast: boolean = true, forceRefresh: boolean = false) => {
    // Create a cache key based on the action and params
    const cacheKey = `${action}-${JSON.stringify(params)}`;
    const now = Date.now();

    // Check if we should use cached data
    if (action === 'getStats' && !forceRefresh) {
      const cachedData = apiCacheRef.current[cacheKey];
      if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRATION)) {
        // Use cached data if it's not expired
        if (action === 'getStats') {
          setActivityStats(cachedData.data.stats);
          setIsLoading(false); // Ensure loading state is cleared when using cached data
        }
        return cachedData.data;
      }
    }

    // Check if we should throttle this API call
    const lastCallTime = lastApiCallTimeRef.current[cacheKey] || 0;
    if (!forceRefresh && now - lastCallTime < MIN_API_CALL_INTERVAL) {
      // Skip this call if it's too soon after the last one
      console.log(`Skipping API call to ${action} - too frequent`);

      // If we're loading stats but skipping the API call due to throttling,
      // we should at least use the most recent data we have
      if (action === 'getStats' && activityStats) {
        setIsLoading(false);
      }

      return null;
    }

    // Update the last API call time
    lastApiCallTimeRef.current[cacheKey] = now;

    // Set loading state only if it's not already set
    if (!isLoading) {
      updateLoadingState(true);
    }
    setError(null);

    // Only clear success message for user-initiated actions
    if (showToast) {
      setSuccess(null);
    }

    // Define abort controller for request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout (increased from 10s)

    try {
      // Get userId from localStorage to ensure it's included in the request
      const userId = localStorage.getItem('userId');

      // Check if userId exists
      if (!userId) {
        throw new Error('User ID not found. Please sign in again.');
      }

      // Ensure we're not in an undefined state before making the API call
      if (typeof window === 'undefined') {
        console.log('Skipping API call - window is undefined');
        return null;
      }

      console.log(`Making API request to ${action} with params:`, params);

      // Add userId to the URL as a query parameter for authentication
      const response = await fetch(`/api/admin/activity/manage?userId=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`, // Add Authorization header
          'Cache-Control': forceRefresh ? 'no-cache' : 'max-age=60', // Cache for 1 minute unless forced refresh
          'Pragma': forceRefresh ? 'no-cache' : '',
        },
        body: JSON.stringify({
          action,
          ...params,
        }),
        credentials: 'include', // Include cookies
        signal: controller.signal, // Use abort controller for timeout
      });

      // Clear the timeout since the request completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Cache the response for getStats action
        if (action === 'getStats') {
          apiCacheRef.current[cacheKey] = {
            data,
            timestamp: now
          };

          // For getStats, only show success message if it was a manual refresh
          if (showToast && forceRefresh) {
            setSuccess('Statistics refreshed successfully');

            // Show toast for manual refresh
            const lastToastTime = lastToastTimeRef.current[action] || 0;
            if (now - lastToastTime > MIN_TOAST_INTERVAL) {
              toast.success('Success', 'Statistics refreshed successfully');
              lastToastTimeRef.current[action] = now;
            }
          }
        }
        // For other actions (purge, clearUser, clearType), always show success message
        else if (showToast) {
          setSuccess(data.message || 'Operation completed successfully');

          // Show toast for these actions
          const lastToastTime = lastToastTimeRef.current[action] || 0;
          if (now - lastToastTime > MIN_TOAST_INTERVAL) {
            toast.success('Success', data.message || 'Activity logs managed successfully');
            lastToastTimeRef.current[action] = now;
          }
        }

        if (onComplete && action !== 'getStats') {
          onComplete();
        }

        // If this was a stats request, update the stats state
        if (action === 'getStats') {
          setActivityStats(data.stats);
        }

        return data;
      } else {
        throw new Error(data.error || 'Unknown error occurred');
      }
    } catch (error) {
      // Check if this is an abort error (timeout)
      if (error instanceof DOMException && error.name === 'AbortError') {
        const errorMessage = 'Request timed out. Please try again.';
        if (showToast) {
          setError(errorMessage);
          if (onError) onError(errorMessage);
          toast.error('Error', errorMessage);
        }
        return null;
      }

      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

      // Only show error UI for user-initiated actions
      if (showToast) {
        setError(errorMessage);
        if (onError) {
          onError(errorMessage);
        }
      } else {
        console.error(`Background operation error (${action}):`, errorMessage);
      }

      // Only show error toast if showToast is true and enough time has passed
      if (showToast) {
        const lastErrorTime = lastToastTimeRef.current['error'] || 0;

        if (now - lastErrorTime > MIN_TOAST_INTERVAL) {
          toast.error('Error', errorMessage);
          lastToastTimeRef.current['error'] = now;
        }
      }

      return null;
    } finally {
      // Clear the timeout if it wasn't already cleared
      clearTimeout(timeoutId);
      updateLoadingState(false);
    }
  }, [activityStats, isLoading, onComplete, onError, toast, updateLoadingState]);

  // Estimate logs to be deleted (only use existing stats, don't make API calls)
  const estimateLogsToDelete = useCallback(() => {
    try {
      // Only estimate if we have stats and we're on the purge tab
      if (activityStats && activeTab === 'purge') {
        // Simple estimate based on age distribution
        const timeDistribution = activityStats.timeDistribution || [];
        let count = 0;

        // Calculate date threshold
        const thresholdDate = new Date();
        thresholdDate.setDate(thresholdDate.getDate() - olderThan);

        // Count logs in the distribution that would be purged
        for (const item of timeDistribution) {
          const itemDate = new Date(item.date);
          if (itemDate < thresholdDate) {
            count += item.count;
          }
        }

        setEstimatedLogsToDelete(count);
      } else {
        // If no stats available or not on purge tab, set to 0
        setEstimatedLogsToDelete(0);
      }
    } catch (error) {
      console.error('Error estimating logs to delete:', error);
      setEstimatedLogsToDelete(0);
    }
  }, [activityStats, activeTab, olderThan]);

  // Load initial activity stats when component mounts (only once)
  useEffect(() => {
    let isMounted = true;
    console.log("UserActivityManagement component mounted");

    // Initial load of activity stats
    const loadInitialStats = async () => {
      try {
        if (isMounted) updateLoadingState(true);
        if (isMounted) setError(null);

        console.log("Loading initial stats");

        // Get userId from localStorage
        const userId = localStorage.getItem('userId');
        if (!userId) {
          throw new Error('User ID not found. Please sign in again.');
        }

        // Use the existing manageActivities function with caching
        const result = await manageActivities('getStats', {}, false, false);

        if (result && result.stats && isMounted) {
          setActivityStats(result.stats);
          console.log("Stats loaded successfully");
        }
      } catch (error) {
        console.error('Error loading initial stats:', error);
        if (isMounted) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load activity statistics';
          handleError(errorMessage);
        }
      } finally {
        if (isMounted) updateLoadingState(false);
      }
    };

    // Load stats only once when component mounts
    loadInitialStats();

    return () => {
      isMounted = false;
      console.log("UserActivityManagement cleanup");
    };
  }, []); // Empty dependency array - only run once on mount

  // Handle tab changes and estimate logs when needed (without API calls)
  useEffect(() => {
    if (activeTab === 'purge' && activityStats) {
      estimateLogsToDelete();
    }
  }, [activeTab, activityStats, estimateLogsToDelete]);

  // Handle olderThan changes for estimation (with debouncing)
  useEffect(() => {
    if (activeTab === 'purge' && activityStats) {
      const timeoutId = setTimeout(() => {
        estimateLogsToDelete();
      }, 500); // 500ms debounce

      return () => clearTimeout(timeoutId);
    }
  }, [olderThan, activeTab, activityStats, estimateLogsToDelete]);

  // Handle form submission based on active tab
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Update loading state
    updateLoadingState(true);

    try {
      switch (activeTab) {
        case 'purge':
          // Always show toast and force refresh for manual actions
          await manageActivities('purge', { days: olderThan }, true, true);
          // Clear the cache for stats after a purge operation
          delete apiCacheRef.current['getStats-{}'];
          break;
        case 'user':
          if (!userIdToClear.trim()) {
            setError('User ID is required');
            updateLoadingState(false);
            return;
          }
          // Always show toast and force refresh for manual actions
          await manageActivities('clearUser', { userId: userIdToClear }, true, true);
          // Clear the cache for stats after a clear operation
          delete apiCacheRef.current['getStats-{}'];
          break;
        case 'type':
          if (!activityType.trim()) {
            setError('Activity type is required');
            updateLoadingState(false);
            return;
          }
          // Always show toast and force refresh for manual actions
          await manageActivities('clearType', { type: activityType }, true, true);
          // Clear the cache for stats after a clear operation
          delete apiCacheRef.current['getStats-{}'];
          break;
        case 'stats':
          // Always show toast and force refresh for manual actions
          await manageActivities('getStats', {}, true, true);
          break;
        default:
          setError('Invalid action');
          updateLoadingState(false);
      }
    } catch (error) {
      console.error('Error in form submission:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
      if (onError) onError(errorMessage);
    } finally {
      // Make sure loading state is updated even if manageActivities throws an error
      updateLoadingState(false);
    }
  };

  // Timer for auto-clearing success messages
  const successTimerRef = React.useRef<NodeJS.Timeout | null>(null);

  // Auto-clear success message after a delay
  React.useEffect(() => {
    // Clear any existing timer
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
    }

    // If there's a success message, set a timer to clear it
    if (success) {
      successTimerRef.current = setTimeout(() => {
        setSuccess(null);
      }, 5000); // Clear after 5 seconds
    }

    // Cleanup
    return () => {
      if (successTimerRef.current) {
        clearTimeout(successTimerRef.current);
      }
    };
  }, [success]);

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg sm:text-xl">User Activity Management</CardTitle>
        <CardDescription className="text-xs sm:text-sm">
          Manage and analyze user activity logs in your system
        </CardDescription>
      </CardHeader>

      <CardContent className="p-3 sm:p-6">
        <Tabs value={activeTab} onValueChange={(value) => {
          console.log("Management tab changed to:", value);
          setActiveTab(value);
          // Reset any errors when switching tabs
          setError(null);
          setSuccess(null);
        }}>
          {/* Mobile-optimized tabs */}
          <div className="block sm:hidden mb-4">
            <TabsList className="grid grid-cols-2 gap-1 h-auto p-1">
              <TabsTrigger value="purge" className="flex flex-col items-center py-2 px-1 text-xs">
                <Trash2 className="h-3 w-3 mb-1" />
                <span>Purge</span>
              </TabsTrigger>
              <TabsTrigger value="user" className="flex flex-col items-center py-2 px-1 text-xs">
                <Trash2 className="h-3 w-3 mb-1" />
                <span>By User</span>
              </TabsTrigger>
            </TabsList>
            <TabsList className="grid grid-cols-2 gap-1 h-auto p-1 mt-2">
              <TabsTrigger value="type" className="flex flex-col items-center py-2 px-1 text-xs">
                <Trash2 className="h-3 w-3 mb-1" />
                <span>By Type</span>
              </TabsTrigger>
              <TabsTrigger value="stats" className="flex flex-col items-center py-2 px-1 text-xs">
                <BarChart3 className="h-3 w-3 mb-1" />
                <span>Stats</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Desktop tabs */}
          <TabsList className="hidden sm:grid grid-cols-4 mb-6">
            <TabsTrigger value="purge" className="flex items-center">
              <Trash2 className="h-4 w-4 mr-2" />
              Purge Old Logs
            </TabsTrigger>
            <TabsTrigger value="user" className="flex items-center">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear By User
            </TabsTrigger>
            <TabsTrigger value="type" className="flex items-center">
              <Trash2 className="h-4 w-4 mr-2" />
              Clear By Type
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              Statistics
            </TabsTrigger>
          </TabsList>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4 border-green-500/50 bg-green-500/10">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <AlertTitle className="text-green-500">Success</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <TabsContent value="purge" className="space-y-3 sm:space-y-4">
              <div className="space-y-3">
                <Label htmlFor="olderThan" className="text-sm font-medium">
                  Purge logs older than {olderThan} days
                </Label>
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <Slider
                    id="olderThan"
                    min={1}
                    max={365}
                    step={1}
                    value={[olderThan]}
                    onValueChange={(values) => {
                      // Update the UI immediately - estimation will be handled by useEffect
                      setOlderThan(values[0]);
                    }}
                    className="flex-1"
                  />
                  <span className="w-10 sm:w-12 text-center text-sm font-medium">{olderThan}d</span>
                </div>

                <div className="pt-2 sm:pt-4">
                  {estimatedLogsToDelete !== null ? (
                    <Alert variant={estimatedLogsToDelete > 100 ? "warning" : "default"} className="mb-3 sm:mb-4">
                      <AlertTriangle className={`h-4 w-4 ${estimatedLogsToDelete > 100 ? "text-amber-500" : ""}`} />
                      <AlertTitle className="text-sm">Estimated Impact</AlertTitle>
                      <AlertDescription className="text-xs sm:text-sm">
                        {estimatedLogsToDelete === 0 ? (
                          <span>No logs found that match the criteria. Try adjusting the time period.</span>
                        ) : (
                          <span>Approximately <strong>{estimatedLogsToDelete.toLocaleString()}</strong> logs will be permanently deleted.</span>
                        )}
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Skeleton className="h-12 sm:h-16 w-full" />
                  )}
                </div>

                <Alert className="mb-3 sm:mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle className="text-sm">Warning</AlertTitle>
                  <AlertDescription className="text-xs sm:text-sm">
                    This action will permanently delete activity logs and cannot be undone.
                  </AlertDescription>
                </Alert>
              </div>

              <Button
                type="submit"
                disabled={isLoading || olderThan < 1 || estimatedLogsToDelete === 0}
                className="w-full bg-red-600 hover:bg-red-700 h-10 sm:h-11 text-sm"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                    <span className="hidden sm:inline">Processing...</span>
                    <span className="sm:hidden">Processing</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">
                      Purge {estimatedLogsToDelete ? estimatedLogsToDelete.toLocaleString() : ''} Logs Older Than {olderThan} Days
                    </span>
                    <span className="sm:hidden">
                      Purge {estimatedLogsToDelete ? estimatedLogsToDelete.toLocaleString() : ''} Logs
                    </span>
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="user" className="space-y-3 sm:space-y-4">
              <div className="space-y-3">
                <Label htmlFor="userId" className="text-sm font-medium">User ID</Label>
                <Input
                  id="userId"
                  placeholder="Enter user ID"
                  value={userIdToClear}
                  onChange={(e) => setUserIdToClear(e.target.value)}
                  required
                  className="h-9 sm:h-10 text-sm"
                />

                <Alert className="mb-3 sm:mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle className="text-sm">Warning</AlertTitle>
                  <AlertDescription className="text-xs sm:text-sm">
                    This action will permanently delete all activity logs for this user and cannot be undone.
                  </AlertDescription>
                </Alert>
              </div>

              <Button
                type="submit"
                disabled={isLoading || !userIdToClear.trim()}
                className="w-full bg-red-600 hover:bg-red-700 h-10 sm:h-11 text-sm"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                    <span className="hidden sm:inline">Processing...</span>
                    <span className="sm:hidden">Processing</span>
                  </>
                ) : (
                  <>Clear User Logs</>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="type" className="space-y-3 sm:space-y-4">
              <div className="space-y-3">
                <Label htmlFor="activityType" className="text-sm font-medium">Activity Type</Label>
                <Select
                  value={activityType}
                  onValueChange={setActivityType}
                >
                  <SelectTrigger className="h-9 sm:h-10">
                    <SelectValue placeholder="Select activity type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auth">Authentication</SelectItem>
                    <SelectItem value="content">Content</SelectItem>
                    <SelectItem value="profile">Profile</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>

                <Alert className="mb-3 sm:mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle className="text-sm">Warning</AlertTitle>
                  <AlertDescription className="text-xs sm:text-sm">
                    This action will permanently delete all activity logs of this type and cannot be undone.
                  </AlertDescription>
                </Alert>
              </div>

              <Button
                type="submit"
                disabled={isLoading || !activityType.trim()}
                className="w-full bg-red-600 hover:bg-red-700 h-10 sm:h-11 text-sm"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                    <span className="hidden sm:inline">Processing...</span>
                    <span className="sm:hidden">Processing</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">
                      Clear {activityType ? activityType.charAt(0).toUpperCase() + activityType.slice(1) : ''} Activity Logs
                    </span>
                    <span className="sm:hidden">
                      Clear {activityType ? activityType.charAt(0).toUpperCase() + activityType.slice(1) : ''} Logs
                    </span>
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="stats" className="space-y-3 sm:space-y-4">
              {isLoading && !activityStats ? (
                <div className="space-y-3 sm:space-y-4">
                  <Skeleton className="h-6 sm:h-8 w-full" />
                  <Skeleton className="h-24 sm:h-32 w-full" />
                  <Skeleton className="h-24 sm:h-32 w-full" />
                </div>
              ) : activityStats ? (
                <div className="space-y-4 sm:space-y-6">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                    <h3 className="text-base sm:text-lg font-medium">Activity Log Statistics</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => manageActivities('getStats', {}, true, true)} // Force refresh on manual click
                      className="w-full sm:w-auto h-8 sm:h-9 text-xs sm:text-sm"
                    >
                      <RefreshCw className={`mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 ${isLoading ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>

                  {/* Mobile: Stack cards vertically */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <Card className="overflow-hidden">
                      <CardHeader className="pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
                        <CardTitle className="text-xs sm:text-sm">Activity Types</CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
                        <ul className="space-y-1 sm:space-y-2">
                          {activityStats.byType.map((item: ActivityStatsItem, index: number) => (
                            <li key={index} className="flex justify-between items-center">
                              <span className="font-medium text-xs sm:text-sm truncate mr-2">{item._id || 'Unknown'}</span>
                              <span className="text-xs sm:text-sm text-vista-light/70 shrink-0">{item.count.toLocaleString()}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card className="overflow-hidden">
                      <CardHeader className="pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
                        <CardTitle className="text-xs sm:text-sm">Activity Actions</CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
                        <ul className="space-y-1 sm:space-y-2">
                          {activityStats.byAction.map((item: ActivityStatsItem, index: number) => (
                            <li key={index} className="flex justify-between items-center">
                              <span className="font-medium text-xs sm:text-sm truncate mr-2">{item._id || 'Unknown'}</span>
                              <span className="text-xs sm:text-sm text-vista-light/70 shrink-0">{item.count.toLocaleString()}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>

                  <Card className="overflow-hidden">
                    <CardHeader className="pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
                      <CardTitle className="text-xs sm:text-sm">Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="text-center sm:text-left">
                          <p className="text-xs sm:text-sm text-vista-light/70">Total Logs</p>
                          <p className="text-xl sm:text-2xl font-bold">{activityStats.totalCount.toLocaleString()}</p>
                        </div>
                        <div className="text-center sm:text-left">
                          <p className="text-xs sm:text-sm text-vista-light/70">Oldest Log</p>
                          <p className="text-xs sm:text-sm">
                            {activityStats.oldestLog
                              ? new Date(activityStats.oldestLog).toLocaleDateString()
                              : 'No logs found'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Button
                  type="button" // Change to button type to prevent form submission
                  disabled={isLoading}
                  onClick={() => manageActivities('getStats', {}, true, true)} // Force refresh on manual click
                  className="w-full h-10 sm:h-11 text-sm"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                      <span className="hidden sm:inline">Processing...</span>
                      <span className="sm:hidden">Processing</span>
                    </>
                  ) : (
                    <>
                      <span className="hidden sm:inline">Get Activity Statistics</span>
                      <span className="sm:hidden">Get Statistics</span>
                    </>
                  )}
                </Button>
              )}
            </TabsContent>
          </form>
        </Tabs>
      </CardContent>

      <CardFooter className="flex justify-center sm:justify-between p-3 sm:p-6">
        <Button
          variant="ghost"
          onClick={() => {
            setActiveTab('stats');
            setError(null);
            setSuccess(null);
          }}
          className="w-full sm:w-auto h-9 sm:h-10 text-sm"
        >
          <span className="hidden sm:inline">View Statistics</span>
          <span className="sm:hidden">Statistics</span>
        </Button>
      </CardFooter>
    </Card>
  );
}