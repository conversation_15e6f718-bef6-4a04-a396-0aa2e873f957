'use client';

import React, { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, UserPlus, BarChart3 } from 'lucide-react';
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

// Simplified activity data interface
interface ActivityData {
  type: string;
  action: string;
  message: string;
  details: string;
  timestamp: string;
  userId?: string;
}

interface UserActivityChartProps {
  data: ActivityData[];
}

export function UserActivityChart({ data = [] }: UserActivityChartProps) {
  // Process activity data for the chart (simplified)
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      console.log('No activity data provided to UserActivityChart');
      return [];
    }

    console.log(`Processing ${data.length} activity items for chart`);

    // Get the last 7 days
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    });

    // Initialize with zero counts for all days
    const dateMap = last7Days.reduce((acc, date) => {
      acc[date] = {
        date,
        logins: 0,
        signups: 0
      };
      return acc;
    }, {} as Record<string, { date: string; logins: number; signups: number }>);

    // Process the activity data
    data.forEach(item => {
      try {
        const itemDate = new Date(item.timestamp);
        if (isNaN(itemDate.getTime())) return;

        const dateStr = itemDate.toISOString().split('T')[0];

        // Only count activities from the last 7 days
        if (dateMap[dateStr]) {
          if (item.type === 'user_login' || (item.type === 'auth' && item.action === 'login')) {
            dateMap[dateStr].logins += 1;
          } else if (item.type === 'user_registration' || (item.type === 'auth' && item.action === 'signup')) {
            dateMap[dateStr].signups += 1;
          }
        }
      } catch (error) {
        console.error('Error processing activity item:', error);
      }
    });

    return Object.values(dateMap);
  }, [data]);

  // Count total metrics
  const loginCount = useMemo(() => {
    return chartData.reduce((sum, day) => sum + day.logins, 0);
  }, [chartData]);

  const signupCount = useMemo(() => {
    return chartData.reduce((sum, day) => sum + day.signups, 0);
  }, [chartData]);



  // Format dates for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <CardContent className="p-4 sm:p-6">
      <div className="space-y-6">
        {/* Simple chart visualization */}
        <div className="h-48 sm:h-64 rounded-lg bg-vista-dark/30 border border-vista-light/10">
          {chartData.length > 0 && (loginCount > 0 || signupCount > 0) ? (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 20,
                  left: 0,
                  bottom: 20,
                }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#374151"
                  opacity={0.3}
                />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  tick={{ fill: '#9CA3AF', fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tick={{ fill: '#9CA3AF', fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  allowDecimals={false}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-vista-dark border border-vista-light/20 rounded-lg p-3 shadow-lg">
                          <p className="text-vista-light font-medium mb-2">{formatDate(label)}</p>
                          {payload.map((entry: any, index: number) => (
                            <p key={index} className="text-sm text-vista-light/80">
                              {entry.dataKey === 'logins' ? 'Logins' : 'Signups'}: {entry.value}
                            </p>
                          ))}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="logins"
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="signups"
                  fill="#10b981"
                  radius={[4, 4, 0, 0]}
                />
              </RechartsBarChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto text-vista-light/30 mb-3" />
                <p className="text-vista-light/60 text-sm">No activity data available</p>
                <p className="text-vista-light/40 text-xs mt-1">User activity will appear here</p>
              </div>
            </div>
          )}
        </div>

        {/* Summary stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-vista-dark/50 border border-vista-light/10 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Users className="h-4 w-4 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-vista-light/80 font-medium">Logins</p>
                <p className="text-xs text-vista-light/50">Last 7 days</p>
              </div>
            </div>
            <p className="text-2xl font-bold text-vista-light">{loginCount}</p>
          </div>

          <div className="bg-vista-dark/50 border border-vista-light/10 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-emerald-500/20 rounded-lg">
                <UserPlus className="h-4 w-4 text-emerald-400" />
              </div>
              <div>
                <p className="text-sm text-vista-light/80 font-medium">Signups</p>
                <p className="text-xs text-vista-light/50">Last 7 days</p>
              </div>
            </div>
            <p className="text-2xl font-bold text-vista-light">{signupCount}</p>
          </div>
        </div>
      </div>
    </CardContent>
  );
}
