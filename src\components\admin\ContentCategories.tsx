'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  Plus,
  Edit,
  Trash2,
  Save,
  Tag,
  Folder,
  FolderPlus
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useConfirmationDialog } from './ConfirmationDialog';
import { useAuth } from '@/contexts/AuthContext';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  count: number;
}

interface Tag {
  id: string;
  name: string;
  slug: string;
  count: number;
}

export default function ContentCategories() {
  // Auth context
  const { user } = useAuth();

  // State for categories and tags
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for category form
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: ''
  });

  // State for tag form
  const [isTagModalOpen, setIsTagModalOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [tagForm, setTagForm] = useState({
    name: ''
  });

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { openDialog, ConfirmationDialog: CategoryConfirmDialog } = useConfirmationDialog();

  // Fetch categories and tags
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare authentication headers and params like banner-ads page
      const authHeaders: Record<string, string> = {
        'Cache-Control': 'no-cache'
      };

      // Add Authorization header if user is available
      if (user?.id) {
        authHeaders['Authorization'] = `Bearer ${user.id}`;
      }

      // Fetch categories from API
      const categoriesUrl = user?.id ? `/api/admin/categories?userId=${user.id}` : '/api/admin/categories';
      const categoriesResponse = await fetch(categoriesUrl, {
        credentials: 'include',
        headers: authHeaders
      });
      if (!categoriesResponse.ok) {
        const errorData = await categoriesResponse.json();
        throw new Error(errorData.error || `Failed to fetch categories (${categoriesResponse.status})`);
      }
      const categoriesData = await categoriesResponse.json();

      // Fetch tags from API
      const tagsUrl = user?.id ? `/api/admin/tags?userId=${user.id}` : '/api/admin/tags';
      const tagsResponse = await fetch(tagsUrl, {
        credentials: 'include',
        headers: authHeaders
      });
      if (!tagsResponse.ok) {
        const errorData = await tagsResponse.json();
        throw new Error(errorData.error || `Failed to fetch tags (${tagsResponse.status})`);
      }
      const tagsData = await tagsResponse.json();

      setCategories(categoriesData.categories || []);
      setTags(tagsData.tags || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Handle opening category modal
  const handleAddCategory = () => {
    setEditingCategory(null);
    setCategoryForm({ name: '', description: '' });
    setIsCategoryModalOpen(true);
  };

  // Handle editing category
  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setCategoryForm({
      name: category.name,
      description: category.description || ''
    });
    setIsCategoryModalOpen(true);
  };

  // Handle category form change
  const handleCategoryFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCategoryForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle category form submission
  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryForm.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Category name is required',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate slug from name if not provided
      const slug = categoryForm.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

      if (editingCategory) {
        // Prepare authentication headers
        const authHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        };

        if (user?.id) {
          authHeaders['Authorization'] = `Bearer ${user.id}`;
        }

        // Update existing category via API
        const updateUrl = user?.id ? `/api/admin/categories/${editingCategory.id}?userId=${user.id}` : `/api/admin/categories/${editingCategory.id}`;
        const response = await fetch(updateUrl, {
          method: 'PUT',
          credentials: 'include',
          headers: authHeaders,
          body: JSON.stringify({
            name: categoryForm.name,
            slug,
            description: categoryForm.description
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to update category (${response.status})`);
        }

        const updatedCategory = await response.json();

        // Update local state
        setCategories(prev =>
          prev.map(cat =>
            cat.id === editingCategory.id ? updatedCategory : cat
          )
        );

        toast({
          title: 'Category Updated',
          description: `"${categoryForm.name}" has been updated successfully.`,
          variant: 'success'
        });
      } else {
        // Prepare authentication headers
        const authHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        };

        if (user?.id) {
          authHeaders['Authorization'] = `Bearer ${user.id}`;
        }

        // Add new category via API
        const createUrl = user?.id ? `/api/admin/categories?userId=${user.id}` : '/api/admin/categories';
        const response = await fetch(createUrl, {
          method: 'POST',
          credentials: 'include',
          headers: authHeaders,
          body: JSON.stringify({
            name: categoryForm.name,
            slug,
            description: categoryForm.description
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to create category (${response.status})`);
        }

        const newCategory = await response.json();

        // Update local state
        setCategories(prev => [...prev, newCategory]);

        toast({
          title: 'Category Added',
          description: `"${categoryForm.name}" has been added successfully.`,
          variant: 'success'
        });
      }

      // Close modal
      setIsCategoryModalOpen(false);
    } catch (error) {
      console.error('Error saving category:', error);
      toast({
        title: 'Error',
        description: 'Failed to save category',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting category
  const handleDeleteCategory = (categoryId: string) => {
    openDialog({
      title: 'Delete Category',
      description: 'Are you sure you want to delete this category? This action cannot be undone and may affect content organization.',
      confirmText: 'Delete Category',
      variant: 'destructive',
      onConfirm: () => performDeleteCategory(categoryId)
    });
  };

  const performDeleteCategory = async (categoryId: string) => {
    try {
      // Prepare authentication headers
      const authHeaders: Record<string, string> = {
        'Cache-Control': 'no-cache'
      };

      if (user?.id) {
        authHeaders['Authorization'] = `Bearer ${user.id}`;
      }

      // Delete category via API
      const deleteUrl = user?.id ? `/api/admin/categories/${categoryId}?userId=${user.id}` : `/api/admin/categories/${categoryId}`;
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        credentials: 'include',
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete category (${response.status})`);
      }

      // Update local state
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));

      toast({
        title: 'Category Deleted',
        description: 'The category has been deleted successfully.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete category',
        variant: 'destructive'
      });
    }
  };

  // Handle opening tag modal
  const handleAddTag = () => {
    setEditingTag(null);
    setTagForm({ name: '' });
    setIsTagModalOpen(true);
  };

  // Handle editing tag
  const handleEditTag = (tag: Tag) => {
    setEditingTag(tag);
    setTagForm({ name: tag.name });
    setIsTagModalOpen(true);
  };

  // Handle tag form change
  const handleTagFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTagForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle tag form submission
  const handleTagSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tagForm.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Tag name is required',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate slug from name
      const slug = tagForm.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

      if (editingTag) {
        // Prepare authentication headers
        const authHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        };

        if (user?.id) {
          authHeaders['Authorization'] = `Bearer ${user.id}`;
        }

        // Update existing tag via API
        const updateUrl = user?.id ? `/api/admin/tags/${editingTag.id}?userId=${user.id}` : `/api/admin/tags/${editingTag.id}`;
        const response = await fetch(updateUrl, {
          method: 'PUT',
          credentials: 'include',
          headers: authHeaders,
          body: JSON.stringify({
            name: tagForm.name,
            slug
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to update tag (${response.status})`);
        }

        const updatedTag = await response.json();

        // Update local state
        setTags(prev =>
          prev.map(tag =>
            tag.id === editingTag.id ? updatedTag : tag
          )
        );

        toast({
          title: 'Tag Updated',
          description: `"${tagForm.name}" has been updated successfully.`,
          variant: 'success'
        });
      } else {
        // Prepare authentication headers
        const authHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        };

        if (user?.id) {
          authHeaders['Authorization'] = `Bearer ${user.id}`;
        }

        // Add new tag via API
        const createUrl = user?.id ? `/api/admin/tags?userId=${user.id}` : '/api/admin/tags';
        const response = await fetch(createUrl, {
          method: 'POST',
          credentials: 'include',
          headers: authHeaders,
          body: JSON.stringify({
            name: tagForm.name,
            slug
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to create tag (${response.status})`);
        }

        const newTag = await response.json();

        // Update local state
        setTags(prev => [...prev, newTag]);

        toast({
          title: 'Tag Added',
          description: `"${tagForm.name}" has been added successfully.`,
          variant: 'success'
        });
      }

      // Close modal
      setIsTagModalOpen(false);
    } catch (error) {
      console.error('Error saving tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to save tag',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting tag
  const handleDeleteTag = (tagId: string) => {
    openDialog({
      title: 'Delete Tag',
      description: 'Are you sure you want to delete this tag? This action cannot be undone and will remove the tag from all associated content.',
      confirmText: 'Delete Tag',
      variant: 'destructive',
      onConfirm: () => performDeleteTag(tagId)
    });
  };

  const performDeleteTag = async (tagId: string) => {
    try {
      // Prepare authentication headers
      const authHeaders: Record<string, string> = {
        'Cache-Control': 'no-cache'
      };

      if (user?.id) {
        authHeaders['Authorization'] = `Bearer ${user.id}`;
      }

      // Delete tag via API
      const deleteUrl = user?.id ? `/api/admin/tags/${tagId}?userId=${user.id}` : `/api/admin/tags/${tagId}`;
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        credentials: 'include',
        headers: authHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete tag (${response.status})`);
      }

      // Update local state
      setTags(prev => prev.filter(tag => tag.id !== tagId));

      toast({
        title: 'Tag Deleted',
        description: 'The tag has been deleted successfully.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete tag',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 max-w-full overflow-hidden">
      <Tabs defaultValue="categories">
        <TabsList className="mb-4 w-full sm:w-auto">
          <TabsTrigger value="categories" className="flex items-center flex-1 sm:flex-none">
            <Folder className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="text-sm sm:text-base">Categories</span>
          </TabsTrigger>
          <TabsTrigger value="tags" className="flex items-center flex-1 sm:flex-none">
            <Tag className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="text-sm sm:text-base">Tags</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories">
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4">
              <div className="min-w-0">
                <CardTitle className="text-vista-light text-lg sm:text-xl">Content Categories</CardTitle>
                <CardDescription className="text-sm">
                  Organize your content with categories
                </CardDescription>
              </div>
              <Button onClick={handleAddCategory} className="w-full sm:w-auto">
                <FolderPlus className="mr-2 h-4 w-4" />
                <span className="text-sm sm:text-base">Add Category</span>
              </Button>
            </CardHeader>
            <CardContent className="px-3 sm:px-6">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-vista-light/50" />
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-500">
                  <p className="text-sm sm:text-base">{error}</p>
                  <Button
                    variant="outline"
                    onClick={fetchData}
                    className="mt-4"
                  >
                    Try Again
                  </Button>
                </div>
              ) : (
                <>
                  {/* Desktop Table View */}
                  <div className="hidden lg:block rounded-md border border-vista-light/10 overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Slug</TableHead>
                          <TableHead>Content Count</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {categories.length > 0 ? (
                          categories.map((category) => (
                            <TableRow key={category.id}>
                              <TableCell className="font-medium">{category.name}</TableCell>
                              <TableCell>{category.description || '-'}</TableCell>
                              <TableCell>
                                <Badge variant="outline">{category.slug}</Badge>
                              </TableCell>
                              <TableCell>{category.count}</TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEditCategory(category)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteCategory(category.id)}
                                    className="text-red-500 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-6 text-vista-light/70">
                              No categories found. Add your first category to get started.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Mobile Card View */}
                  <div className="lg:hidden space-y-3">
                    {categories.length > 0 ? (
                      categories.map((category) => (
                        <div
                          key={category.id}
                          className="bg-vista-dark/50 border border-vista-light/10 rounded-lg p-3 sm:p-4 hover:bg-vista-dark/70 transition-colors"
                        >
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0 space-y-2">
                              <div>
                                <h3 className="font-semibold text-vista-light text-sm sm:text-base line-clamp-1">
                                  {category.name}
                                </h3>
                                {category.description && (
                                  <p className="text-xs sm:text-sm text-vista-light/70 line-clamp-2 mt-1">
                                    {category.description}
                                  </p>
                                )}
                              </div>
                              <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm">
                                <Badge variant="outline" className="text-xs">
                                  {category.slug}
                                </Badge>
                                <span className="text-vista-light/60">
                                  {category.count} items
                                </span>
                              </div>
                            </div>
                            <div className="flex gap-1 flex-shrink-0">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleEditCategory(category)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-500 hover:text-red-700"
                                onClick={() => handleDeleteCategory(category.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-vista-light/70">
                        <Folder className="h-12 w-12 mx-auto mb-4 text-vista-light/30" />
                        <p className="text-sm sm:text-base">No categories found.</p>
                        <p className="text-xs sm:text-sm mt-1">Add your first category to get started.</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tags">
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4">
              <div className="min-w-0">
                <CardTitle className="text-vista-light text-lg sm:text-xl">Content Tags</CardTitle>
                <CardDescription className="text-sm">
                  Tag your content for better discoverability
                </CardDescription>
              </div>
              <Button onClick={handleAddTag} className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                <span className="text-sm sm:text-base">Add Tag</span>
              </Button>
            </CardHeader>
            <CardContent className="px-3 sm:px-6">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-vista-light/50" />
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-500">
                  <p className="text-sm sm:text-base">{error}</p>
                  <Button
                    variant="outline"
                    onClick={fetchData}
                    className="mt-4"
                  >
                    Try Again
                  </Button>
                </div>
              ) : (
                <>
                  {/* Desktop Table View */}
                  <div className="hidden lg:block rounded-md border border-vista-light/10 overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Slug</TableHead>
                          <TableHead>Content Count</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {tags.length > 0 ? (
                          tags.map((tag) => (
                            <TableRow key={tag.id}>
                              <TableCell className="font-medium">{tag.name}</TableCell>
                              <TableCell>
                                <Badge variant="outline">{tag.slug}</Badge>
                              </TableCell>
                              <TableCell>{tag.count}</TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEditTag(tag)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteTag(tag.id)}
                                    className="text-red-500 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-6 text-vista-light/70">
                              No tags found. Add your first tag to get started.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Mobile Card View */}
                  <div className="lg:hidden space-y-3">
                    {tags.length > 0 ? (
                      tags.map((tag) => (
                        <div
                          key={tag.id}
                          className="bg-vista-dark/50 border border-vista-light/10 rounded-lg p-3 sm:p-4 hover:bg-vista-dark/70 transition-colors"
                        >
                          <div className="flex items-center justify-between gap-3">
                            <div className="flex-1 min-w-0 space-y-2">
                              <h3 className="font-semibold text-vista-light text-sm sm:text-base line-clamp-1">
                                {tag.name}
                              </h3>
                              <div className="flex items-center gap-2 text-xs sm:text-sm">
                                <Badge variant="outline" className="text-xs">
                                  {tag.slug}
                                </Badge>
                                <span className="text-vista-light/60">
                                  {tag.count} items
                                </span>
                              </div>
                            </div>
                            <div className="flex gap-1 flex-shrink-0">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleEditTag(tag)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-500 hover:text-red-700"
                                onClick={() => handleDeleteTag(tag.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-vista-light/70">
                        <Tag className="h-12 w-12 mx-auto mb-4 text-vista-light/30" />
                        <p className="text-sm sm:text-base">No tags found.</p>
                        <p className="text-xs sm:text-sm mt-1">Add your first tag to get started.</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Category Form Modal */}
      <Dialog open={isCategoryModalOpen} onOpenChange={setIsCategoryModalOpen}>
        <DialogContent className="w-[95vw] max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg sm:text-xl">
              {editingCategory ? 'Edit Category' : 'Add New Category'}
            </DialogTitle>
            <DialogDescription className="text-sm">
              {editingCategory
                ? 'Update the category details below.'
                : 'Create a new category to organize your content.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleCategorySubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">Category Name</Label>
              <Input
                id="name"
                name="name"
                value={categoryForm.name}
                onChange={handleCategoryFormChange}
                placeholder="Enter category name"
                className="text-sm"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">Description (Optional)</Label>
              <Input
                id="description"
                name="description"
                value={categoryForm.description}
                onChange={handleCategoryFormChange}
                placeholder="Enter category description"
                className="text-sm"
              />
            </div>

            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCategoryModalOpen(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                <span className="text-sm">
                  {editingCategory ? 'Update Category' : 'Create Category'}
                </span>
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Tag Form Modal */}
      <Dialog open={isTagModalOpen} onOpenChange={setIsTagModalOpen}>
        <DialogContent className="w-[95vw] max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg sm:text-xl">
              {editingTag ? 'Edit Tag' : 'Add New Tag'}
            </DialogTitle>
            <DialogDescription className="text-sm">
              {editingTag
                ? 'Update the tag details below.'
                : 'Create a new tag to label your content.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleTagSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tagName" className="text-sm font-medium">Tag Name</Label>
              <Input
                id="tagName"
                name="name"
                value={tagForm.name}
                onChange={handleTagFormChange}
                placeholder="Enter tag name"
                className="text-sm"
                required
              />
            </div>

            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsTagModalOpen(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                <span className="text-sm">
                  {editingTag ? 'Update Tag' : 'Create Tag'}
                </span>
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <CategoryConfirmDialog />
    </div>
  );
}
