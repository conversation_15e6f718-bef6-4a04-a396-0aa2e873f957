'use client';

import { useState, useEffect, Fragment } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { 
  Loader2, UploadCloud, X, Eye, EyeOff, ArrowLeft, 
  ArrowRight, Check, UserPlus, Mail, Lock, ShieldCheck 
} from 'lucide-react';
import { UserAvatar } from '@/components/UserAvatar';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '@/hooks/use-media-query';

interface UserFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editUser?: {
    id: string;
    name: string;
    email: string;
    role: string;
    emailVerified?: Date | null;
    profileImage?: string;
    picture?: string;
  };
}

export default function UserFormModal({
  isOpen,
  onClose,
  onSuccess,
  editUser
}: UserFormModalProps) {
  const isEditing = !!editUser;
  const { user: currentUser } = useAuth();
  const isMobile = useMediaQuery("(max-width: 640px)");
  const [currentStep, setCurrentStep] = useState(0);
  
  // Mobile steps
  const STEPS = [
    { 
      id: 'basic', 
      title: 'Basic Info',
      icon: UserPlus
    },
    { 
      id: 'credentials', 
      title: 'Contact',
      icon: Mail
    },
    { 
      id: 'security', 
      title: 'Security',
      icon: ShieldCheck
    },
  ];

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user',
    emailVerified: false
  });
  
  // Password visibility states
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // State for image upload
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  
  // Reset step when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
    }
  }, [isOpen]);

  // Update form data and reset image state when editing a user or opening the modal
  useEffect(() => {
    if (isOpen) {
      if (isEditing && editUser) {
        setFormData({
          name: editUser.name || '',
          email: editUser.email || '',
          password: '',
          confirmPassword: '',
          role: editUser.role || 'user',
          emailVerified: editUser.emailVerified ? true : false
        });
        // Set initial preview to existing image if editing
        const existingImageUrl = editUser.profileImage || editUser.picture;
        setImagePreview(existingImageUrl || null);
        setImageFile(null); // Reset file input
      } else {
        // Reset form when adding a new user
        setFormData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          role: 'user',
          emailVerified: false
        });
        setImagePreview(null);
        setImageFile(null);
      }
    } else {
      // Clean up preview URL when modal closes
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }
      setImagePreview(null);
      setImageFile(null);
    }
  }, [isEditing, editUser, isOpen, imagePreview]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field when user types
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Toggle email verification status
  const handleVerificationToggle = (value: boolean) => {
    setFormData(prev => ({ ...prev, emailVerified: value }));
  };

  // Validate form - now with step validation
  const validateForm = (specificStep?: number) => {
    const newErrors: Record<string, string> = {};
    const validateAll = specificStep === undefined;
    
    // Step 0 validation (Basic Info) or validate all
    if (validateAll || specificStep === 0) {
      if (!formData.name.trim()) {
        newErrors.name = 'Name is required';
      }
    }
    
    // Step 1 validation (Credentials) or validate all
    if (validateAll || specificStep === 1) {
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid';
      }
    }
    
    // Step 2 validation (Security) or validate all
    if (!isEditing && (validateAll || specificStep === 2)) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Check if current step is valid and navigate to next step
  const handleNextStep = () => {
    const isStepValid = validateForm(currentStep);
    if (isStepValid && currentStep < STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };
  
  // Navigate to previous step
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // For mobile multi-step form, proceed to next step or submit on last step
    if (isMobile && currentStep < STEPS.length - 1) {
      handleNextStep();
      return;
    }

    // Full validation for final submission
    if (!validateForm()) {
      return;
    }
    
    if (!currentUser?.id) {
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in as an admin to perform this action.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    let uploadedImageUrl: string | null = null;
    const oldImageUrl: string | null = (isEditing && (editUser?.profileImage || editUser?.picture)) || null;
    let imageUpdateType: 'new' | 'remove' | 'none' = 'none';

    try {
      // 1. Handle Image Upload/Removal
      if (imageFile) {
        // New image selected for upload
        setIsUploading(true);
        try {
          const formData = new FormData();
          formData.append('file', imageFile);
          formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || 'streamvista_profiles'); // Use unsigned preset
          formData.append('cloud_name', process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '');

          const cloudinaryUrl = `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`;

          const uploadResponse = await fetch(cloudinaryUrl, {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json();
            throw new Error(`Cloudinary upload failed: ${errorData.error?.message || 'Unknown error'}`);
          }

          const uploadData = await uploadResponse.json();
          uploadedImageUrl = uploadData.secure_url;
          imageUpdateType = 'new';

        } catch (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast({
            title: 'Image Upload Failed',
            description: uploadError instanceof Error ? uploadError.message : 'Could not upload image.',
            variant: 'destructive',
          });
          setIsUploading(false);
          setIsSubmitting(false);
          return; // Stop submission if upload fails
        } finally {
          setIsUploading(false);
        }
      } else if (isEditing && oldImageUrl && !imagePreview) {
        // Image was present but removed by the user
        uploadedImageUrl = null; // Explicitly set to null for removal
        imageUpdateType = 'remove';
      }

      // 2. Prepare data for User API
      interface UserApiData {
        name: string;
        email: string;
        role: string;
        emailVerified: string | null;
        password?: string;
        profileImage?: string | null; // Can be new URL or null for removal
        oldProfileImage?: string | null; // To signal deletion on backend
      }

      const apiData: UserApiData = {
        name: formData.name,
        email: formData.email,
        role: formData.role,
        emailVerified: formData.emailVerified ? new Date().toISOString() : null,
        ...(formData.password && !isEditing ? { password: formData.password } : {}),
      };

      if (imageUpdateType === 'new' && uploadedImageUrl) {
        apiData.profileImage = uploadedImageUrl;
        apiData.oldProfileImage = oldImageUrl; // Send old URL for deletion
      } else if (imageUpdateType === 'remove') {
        apiData.profileImage = null; // Signal removal
        apiData.oldProfileImage = oldImageUrl; // Send old URL for deletion
      }

      // 3. Determine API endpoint and method
      const url = isEditing
        ? `/api/admin/users/${editUser.id}?userId=${currentUser.id}`
        : `/api/admin/users?userId=${currentUser.id}`;
      const method = isEditing ? 'PUT' : 'POST';

      // 4. Make API request to save user data
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser.id}`
        },
        body: JSON.stringify({
          ...apiData,
          userId: currentUser.id // Include userId in the request body for authentication
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Attempt to clean up uploaded image if user save failed
        if (imageUpdateType === 'new' && uploadedImageUrl) {
          console.warn('User update failed after image upload. Attempting to delete uploaded image...');
          // TODO: Implement cleanup logic - call a backend route to delete the image from Cloudinary
        }
        throw new Error(errorData.error || 'Failed to save user');
      }

      // Show success message
      toast({
        title: isEditing ? 'User Updated' : 'User Created',
        description: isEditing
          ? `${formData.name}'s profile has been updated successfully.`
          : `${formData.name} has been added to the system.`,
        variant: 'success'
      });

      // Close modal and refresh user list
      onSuccess();
      onClose();

    } catch (error) {
      console.error('Error saving user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      setIsUploading(false); // Ensure uploading state is reset
    }
  };

  // Handle dialog close
  const handleDialogClose = () => {
    // Reset errors when dialog closes
    setErrors({});
    onClose();
  };

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  const renderStepIndicator = () => {
    if (!isMobile) return null;
    
    return (
      <div className="mb-4 mt-2">
        <div className="flex items-center justify-center">
          {STEPS.map((step, index) => (
            <Fragment key={step.id}>
              <div
                className={`flex flex-col items-center ${
                  index <= currentStep ? 'text-vista-blue' : 'text-vista-light/40'
                }`}
              >
                <div
                  className={`
                    flex items-center justify-center h-8 w-8 rounded-full mb-1
                    ${index < currentStep ? 'bg-vista-blue text-white' :
                      index === currentStep ? 'bg-vista-blue/90 text-white border border-vista-blue' :
                      'bg-vista-dark-lighter text-vista-light/60 border border-vista-light/20'}
                  `}
                >
                  {index < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <step.icon className="h-4 w-4" />
                  )}
                </div>
                <span className="text-xs">
                  {step.title}
                </span>
              </div>
              {index < STEPS.length - 1 && (
                <div
                  className={`w-10 h-0.5 mx-1 ${
                    index < currentStep ? 'bg-vista-blue' : 'bg-vista-light/20'
                  }`}
                />
              )}
            </Fragment>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className={`${isMobile ? 'w-[95%] max-w-full' : 'sm:max-w-[500px]'} bg-vista-dark border-vista-light/20`}>
        <DialogHeader>
          <DialogTitle className="text-vista-light">{isEditing ? 'Edit User' : 'Add New User'}</DialogTitle>
          <DialogDescription className="text-vista-light/70">
            {isEditing
              ? 'Update user details and permissions.'
              : 'Create a new user account in the system.'}
          </DialogDescription>
        </DialogHeader>
        
        {renderStepIndicator()}

        <form onSubmit={handleSubmit} className={`space-y-4 py-4 ${isMobile ? 'px-1' : ''}`}>
          <div className="grid grid-cols-1 gap-4">
            <AnimatePresence mode="wait">
              {/* Step 1: Basic Info */}
              {(!isMobile || (isMobile && currentStep === 0)) && (
                <motion.div
                  key="step1"
                  initial={isMobile ? { opacity: 0, x: 20 } : { opacity: 1 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={isMobile ? { opacity: 0, x: -20 } : { opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Profile Image Upload */}
                  <div className="space-y-2">
                    <Label className="text-vista-light font-medium">Profile Image</Label>
                    <div className="flex items-center gap-4">
                      <UserAvatar
                        userId={editUser?.id || 'new'}
                        src={imagePreview || undefined}
                        alt={formData.name || 'User'}
                        fallback={formData.name?.charAt(0)?.toUpperCase() || 'U'}
                        className="h-16 w-16 rounded-full border-2 border-vista-light/30"
                      />
                      <div className="relative flex-grow">
                        <Input
                          id="profileImage"
                          type="file"
                          accept="image/png, image/jpeg, image/webp"
                          onChange={handleImageChange}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                          disabled={isUploading}
                        />
                        <div className="flex items-center justify-center w-full h-16 px-4 border-2 border-dashed border-vista-light/30 rounded-md bg-vista-dark-lighter hover:border-vista-blue transition-colors">
                          {isUploading ? (
                            <Loader2 className="h-6 w-6 text-vista-blue animate-spin" />
                          ) : imagePreview && imagePreview.startsWith('blob:') ? (
                            <span className="text-sm text-vista-light truncate">{imageFile?.name}</span>
                          ) : (
                            <div className="text-center">
                              <UploadCloud className="h-6 w-6 text-vista-light/70 mx-auto mb-1" />
                              <span className="text-sm text-vista-light/70">Click or drag to upload</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {imagePreview && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={handleRemoveImage}
                          className="text-vista-light/70 hover:text-red-500"
                          aria-label="Remove image"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <p className="text-xs text-vista-light/60">Recommended: Square image (PNG, JPG, WEBP). Max 2MB.</p>
                  </div>

                  {/* Name */}
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-vista-light font-medium">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter full name"
                      className={`bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue ${errors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                      autoComplete="off"
                      autoFocus
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Step 2: Email */}
              {(!isMobile || (isMobile && currentStep === 1)) && (
                <motion.div
                  key="step2"
                  initial={isMobile ? { opacity: 0, x: 20 } : { opacity: 1 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={isMobile ? { opacity: 0, x: -20 } : { opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-vista-light font-medium">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className={`bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue ${errors.email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                      autoComplete="off"
                    />
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email}</p>
                    )}
                  </div>
                  
                  {/* Email Verification Status */}
                  <div className="space-y-2">
                    <Label htmlFor="emailVerified" className="text-vista-light font-medium">Email Verification</Label>
                    <Select
                      value={formData.emailVerified ? 'verified' : 'unverified'}
                      onValueChange={(value) => handleVerificationToggle(value === 'verified')}
                    >
                      <SelectTrigger
                        id="emailVerified"
                        className="bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue"
                      >
                        <SelectValue placeholder="Verification status" />
                      </SelectTrigger>
                      <SelectContent className="bg-vista-dark border-vista-light/20">
                        <SelectItem value="verified" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Verified</SelectItem>
                        <SelectItem value="unverified" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Unverified</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-vista-light/60">Set to verified to allow immediate login</p>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Security */}
              {(!isMobile || (isMobile && currentStep === 2)) && (
                <motion.div
                  key="step3"
                  initial={isMobile ? { opacity: 0, x: 20 } : { opacity: 1 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={isMobile ? { opacity: 0, x: -20 } : { opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Role */}
                  <div className="space-y-2">
                    <Label htmlFor="role" className="text-vista-light font-medium">User Role</Label>
                    <Select
                      value={formData.role}
                      onValueChange={(value) => handleSelectChange('role', value)}
                    >
                      <SelectTrigger
                        id="role"
                        className="bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue"
                      >
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent className="bg-vista-dark border-vista-light/20">
                        <SelectItem value="user" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Regular User</SelectItem>
                        <SelectItem value="moderator" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Moderator</SelectItem>
                        <SelectItem value="admin" className="text-vista-light hover:bg-vista-dark-lighter focus:bg-vista-dark-lighter">Administrator</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Password (only for new users) */}
                  {!isEditing && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="password" className="text-vista-light font-medium">Password</Label>
                        <div className="relative">
                          <Input
                            id="password"
                            name="password"
                            type={showPassword ? "text" : "password"}
                            value={formData.password}
                            onChange={handleChange}
                            placeholder="Create a password"
                            className={`bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue pr-10 ${errors.password ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                            autoComplete="new-password"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-0 top-0 h-full px-3 text-vista-light/70 hover:text-vista-light"
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            <span className="sr-only">
                              {showPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                        {errors.password && (
                          <p className="text-sm text-red-500">{errors.password}</p>
                        )}
                        {!errors.password && (
                          <p className="text-xs text-vista-light/60">Password must be at least 8 characters</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword" className="text-vista-light font-medium">Confirm Password</Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={formData.confirmPassword}
                            onChange={handleChange}
                            placeholder="Confirm password"
                            className={`bg-vista-dark-lighter border-vista-light/20 text-vista-light focus:border-vista-blue focus:ring-1 focus:ring-vista-blue pr-10 ${errors.confirmPassword ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                            autoComplete="new-password"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-0 top-0 h-full px-3 text-vista-light/70 hover:text-vista-light"
                          >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            <span className="sr-only">
                              {showConfirmPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                        {errors.confirmPassword && (
                          <p className="text-sm text-red-500">{errors.confirmPassword}</p>
                        )}
                      </div>
                    </>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          <DialogFooter className="gap-2 sm:gap-0">
            {isMobile && (
              <div className="flex w-full sm:w-auto gap-2 justify-between">
                {currentStep > 0 ? (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrevStep}
                    className="flex-1 border-vista-light/20 text-vista-light"
                    disabled={isSubmitting}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                ) : (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleDialogClose}
                    className="flex-1 border-vista-light/20 text-vista-light"
                  >
                    Cancel
                  </Button>
                )}
                
                <Button
                  type="submit"
                  className="flex-1 bg-vista-blue hover:bg-vista-blue/90 text-white"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {currentStep === STEPS.length - 1 ? (
                    isEditing ? 'Update User' : 'Create User'
                  ) : (
                    <>
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            )}
            
            {!isMobile && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleDialogClose}
                  className="border-vista-light/20 text-vista-light hover:bg-vista-dark-lighter hover:text-vista-light"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-vista-blue hover:bg-vista-blue/90 text-white"
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEditing ? 'Update User' : 'Create User'}
                </Button>
              </>
            )}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}