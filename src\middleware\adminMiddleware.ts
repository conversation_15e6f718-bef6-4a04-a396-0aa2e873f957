import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User';
import { ensureMongooseConnection } from '@/lib/mongodb';

/**
 * Middleware to protect admin routes by checking admin privileges
 * This middleware is meant to be used within API route handlers
 */
export async function checkAdminRole(userId: string): Promise<boolean> {
  try {
    if (!userId) {
      return false;
    }

    // Connect to MongoDB
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId);

    if (!user) {
      return false;
    }

    // Check if user has admin role
    const userRole = user.role || 'user';
    return userRole === 'admin' || userRole === 'superadmin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Middleware to protect admin routes
 * This can be used in API route handlers
 */
export async function adminMiddleware(request: NextRequest) {
  try {
    // Get the userId from cookies
    const userId = request.cookies.get('userId')?.value;

    if (!userId) {
      // Redirect to login if not authenticated
      return {
        success: false,
        response: NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      };
    }

    // Check if user has admin role
    const isUserAdmin = await checkAdminRole(userId);

    if (!isUserAdmin) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'Admin privileges required' },
          { status: 403 }
        )
      };
    }
    // User is authenticated and has admin role
    return { success: true };
  } catch (error) {
    console.error('Admin middleware error:', error);
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Server error during authorization' },
        { status: 500 }
      )
    };
  }
}
