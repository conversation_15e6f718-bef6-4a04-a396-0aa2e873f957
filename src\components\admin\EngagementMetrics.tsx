'use client';

import { useMemo } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> 
} from '@/components/ui/charts';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Ta<PERSON>, 
  <PERSON>bs<PERSON>ontent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Activity, 
  TrendingUp, 
  Users, 
  UserCheck,
  UserMinus,
  Timer,
  MousePointerClick,
  Repeat,
  ArrowRight,
  Share2
} from 'lucide-react';

interface EngagementData {
  // Session metrics
  sessionDuration: {
    average: number;
    byDate: Array<{
      date: string;
      value: number;
    }>;
    distribution: Array<{
      range: string;
      count: number;
    }>;
  };
  
  // Pages per visit
  pagesPerVisit: {
    average: number;
    byDate: Array<{
      date: string;
      value: number;
    }>;
    distribution: Array<{
      range: string;
      count: number;
    }>;
  };
  
  // Visitor loyalty
  visitorLoyalty: {
    newVsReturning: {
      new: number;
      returning: number;
    };
    visitFrequency: Array<{
      visits: string;
      count: number;
    }>;
  };
  
  // Retention data
  retention: {
    overall: number;
    byWeek: Array<{
      week: string;
      rate: number;
    }>;
  };
}

interface EngagementMetricsProps {
  data: EngagementData;
  isLoading: boolean;
  className?: string;
}

export default function EngagementMetrics({
  data,
  isLoading,
  className = ''
}: EngagementMetricsProps) {
  // Enhanced format time in minutes and seconds with validation
  const formatTime = (seconds: number | undefined) => {
    if (seconds === undefined || seconds === null || isNaN(seconds) || !Number.isFinite(seconds)) {
      return '0m 0s';
    }
    const safeSeconds = Math.max(0, Math.floor(seconds));
    const mins = Math.floor(safeSeconds / 60);
    const secs = safeSeconds % 60;
    return `${mins}m ${secs}s`;
  };

  // Enhanced format percentages with validation
  const formatPercent = (value: number | undefined) => {
    if (value === undefined || value === null || isNaN(value) || !Number.isFinite(value)) {
      return '0.0%';
    }
    const safeValue = Math.max(0, Math.min(1, value));
    return `${(safeValue * 100).toFixed(1)}%`;
  };
  
  // Enhanced safely get total from distribution array
  const getDistributionTotal = (distribution: Array<{range: string, count: number}> | undefined) => {
    if (!distribution || !Array.isArray(distribution) || distribution.length === 0) {
      return 0;
    }
    return distribution.reduce((sum, item) => {
      const count = typeof item?.count === 'number' && Number.isFinite(item.count) ? Math.max(0, item.count) : 0;
      return sum + count;
    }, 0);
  };

  // Enhanced safely get percentage from distribution array
  const getDistributionPercent = (distribution: Array<{range: string, count: number}> | undefined, index: number) => {
    if (!distribution || !Array.isArray(distribution) || distribution.length === 0 || index < 0 || index >= distribution.length || !distribution[index]) {
      return 0;
    }
    const total = getDistributionTotal(distribution);
    const itemCount = typeof distribution[index]?.count === 'number' && Number.isFinite(distribution[index].count)
      ? Math.max(0, distribution[index].count)
      : 0;
    return total > 0 ? itemCount / total : 0;
  };
  
  // Process session duration data for the chart
  const sessionDurationData = useMemo(() => {
    if (!data?.sessionDuration?.byDate || !Array.isArray(data.sessionDuration.byDate)) {
      return [];
    }
    return data.sessionDuration.byDate.map(item => ({
      date: item.date,
      duration: item.value
    }));
  }, [data?.sessionDuration?.byDate]);
  
  // Process pages per visit data for the chart
  const pagesPerVisitData = useMemo(() => {
    if (!data?.pagesPerVisit?.byDate || !Array.isArray(data.pagesPerVisit.byDate)) {
      return [];
    }
    return data.pagesPerVisit.byDate.map(item => ({
      date: item.date,
      pages: item.value
    }));
  }, [data?.pagesPerVisit?.byDate]);
  
  // Process visitor loyalty data for the pie chart
  const visitorLoyaltyData = useMemo(() => {
    if (!data?.visitorLoyalty?.newVsReturning) {
      return [
        { name: 'New', value: 0 },
        { name: 'Returning', value: 0 }
      ];
    }
    return [
      { name: 'New', value: data.visitorLoyalty.newVsReturning.new || 0 },
      { name: 'Returning', value: data.visitorLoyalty.newVsReturning.returning || 0 }
    ];
  }, [data?.visitorLoyalty?.newVsReturning]);
  
  // Process visit frequency data for the bar chart
  const visitFrequencyData = useMemo(() => {
    if (!data?.visitorLoyalty?.visitFrequency || !Array.isArray(data.visitorLoyalty.visitFrequency)) {
      return [];
    }
    return data.visitorLoyalty.visitFrequency.map(item => ({
      visits: item.visits,
      count: item.count
    }));
  }, [data?.visitorLoyalty?.visitFrequency]);
  
  // Process retention data for the line chart
  const retentionData = useMemo(() => {
    if (!data?.retention?.byWeek || !Array.isArray(data.retention.byWeek)) {
      return [];
    }
    return data.retention.byWeek.map(item => ({
      week: item.week,
      rate: item.rate
    }));
  }, [data?.retention?.byWeek]);
  
  // Mobile-optimized skeleton loader
  if (isLoading) {
    return (
      <Card className={`border shadow-md ${className}`}>
        <CardHeader>
          <div className="h-5 sm:h-6 w-40 sm:w-48 bg-muted animate-pulse rounded"></div>
          <div className="h-3 sm:h-4 w-48 sm:w-64 bg-muted/60 animate-pulse rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="h-24 sm:h-28 bg-muted/30 animate-pulse rounded"></div>
              <div className="h-24 sm:h-28 bg-muted/30 animate-pulse rounded"></div>
            </div>
            <div className="h-[200px] sm:h-[250px] w-full bg-muted/50 animate-pulse rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={`border shadow-md ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
          <Activity className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
          <span className="hidden sm:inline">Visitor Engagement Metrics</span>
          <span className="sm:hidden">Engagement</span>
        </CardTitle>
        <CardDescription className="text-xs sm:text-sm">
          Detailed analysis of how visitors interact with your content
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="session" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="session" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <Timer className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Session Metrics</span>
              <span className="sm:hidden">Session</span>
            </TabsTrigger>
            <TabsTrigger value="loyalty" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <Repeat className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Visitor Loyalty</span>
              <span className="sm:hidden">Loyalty</span>
            </TabsTrigger>
            <TabsTrigger value="retention" className="flex items-center gap-1.5 text-xs sm:text-sm">
              <UserCheck className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Retention</span>
              <span className="sm:hidden">Retention</span>
            </TabsTrigger>
          </TabsList>
          
          {/* Session Metrics Tab - Mobile Optimized */}
          <TabsContent value="session" className="space-y-6">
            {/* Summary Cards - Mobile Responsive */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="py-4">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Timer className="h-4 w-4 text-blue-500" />
                    Average Session Duration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl sm:text-3xl font-bold">
                    {formatTime(data?.sessionDuration?.average)}
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground mt-1">
                    Time visitors spend on your site per session
                  </div>
                  
                  <Separator className="my-3" />
                  
                  <div className="grid grid-cols-3 gap-1 sm:gap-2 text-center">
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">Short</div>
                      <div className="text-xs sm:text-sm font-medium">
                        {formatPercent(getDistributionPercent(data?.sessionDuration?.distribution, 0))}
                      </div>
                      <div className="text-xs">0-1m</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">Medium</div>
                      <div className="text-xs sm:text-sm font-medium">
                        {formatPercent(getDistributionPercent(data?.sessionDuration?.distribution, 1))}
                      </div>
                      <div className="text-xs">1-5m</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">Long</div>
                      <div className="text-xs sm:text-sm font-medium">
                        {formatPercent(getDistributionPercent(data?.sessionDuration?.distribution, 2))}
                      </div>
                      <div className="text-xs">5m+</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="py-4">
                  <CardTitle className="text-base flex items-center gap-2">
                    <MousePointerClick className="h-4 w-4 text-purple-500" />
                    Pages Per Visit
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl sm:text-3xl font-bold">
                    {data?.pagesPerVisit?.average ? data.pagesPerVisit.average.toFixed(1) : '0.0'}
                  </div>
                  <div className="text-xs sm:text-sm text-muted-foreground mt-1">
                    Average number of pages viewed in each session
                  </div>
                  
                  <Separator className="my-3" />
                  
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2 text-center">
                    {data?.pagesPerVisit?.distribution && Array.isArray(data.pagesPerVisit.distribution) &&
                      data.pagesPerVisit.distribution.map((item, index) => (
                      <div key={index} className="space-y-1">
                        <div className="text-xs text-muted-foreground">{item.range}</div>
                        <div className="text-xs sm:text-sm font-medium">
                          {formatPercent(getDistributionPercent(data.pagesPerVisit.distribution, index))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Session Duration Trend Chart - Mobile Optimized */}
            <div>
              <h3 className="text-base sm:text-lg font-medium mb-3">Session Duration Trend</h3>
              <div className="h-[200px] sm:h-[250px] w-full overflow-hidden min-w-0">
                {sessionDurationData.length > 0 ? (
                  <LineChart
                    data={sessionDurationData}
                    index="date"
                    categories={['duration']}
                    colors={['#3b82f6']}
                    valueFormatter={(value) => formatTime(value)}
                    showAnimation={true}
                    showLegend={false}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full bg-slate-50 dark:bg-slate-900/50 rounded-md">
                    <p className="text-muted-foreground">No session duration data available</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Pages Per Visit Trend Chart - Mobile Optimized */}
            <div>
              <h3 className="text-base sm:text-lg font-medium mb-3">Pages Per Visit Trend</h3>
              <div className="h-[200px] sm:h-[250px] w-full overflow-hidden min-w-0">
                {pagesPerVisitData.length > 0 ? (
                  <LineChart
                    data={pagesPerVisitData}
                    index="date"
                    categories={['pages']}
                    colors={['#8b5cf6']}
                    valueFormatter={(value) => `${Number.isFinite(value) ? value.toFixed(1) : '0.0'} pages`}
                    showAnimation={true}
                    showLegend={false}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full bg-slate-50 dark:bg-slate-900/50 rounded-md">
                    <p className="text-muted-foreground">No pages per visit data available</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          {/* Visitor Loyalty Tab - Mobile Optimized */}
          <TabsContent value="loyalty" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* New vs Returning Visitors */}
              <div>
                <h3 className="text-base sm:text-lg font-medium mb-3">New vs Returning Visitors</h3>
                <div className="h-[200px] sm:h-[250px] w-full overflow-hidden min-w-0">
                  {visitorLoyaltyData.some(item => item.value > 0) ? (
                    <PieChart
                      data={visitorLoyaltyData}
                      index="name"
                      category="value"
                      colors={['#3b82f6', '#8b5cf6']}
                      valueFormatter={(value) => `${Number.isFinite(value) ? value.toLocaleString() : '0'} visitors`}
                      showAnimation={true}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-slate-50 dark:bg-slate-900/50 rounded-md">
                      <p className="text-muted-foreground">No visitor loyalty data available</p>
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-2 sm:gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-xs sm:text-sm text-muted-foreground">New Visitors</div>
                    <div className="text-lg sm:text-xl font-medium">
                      {formatPercent(
                        data?.visitorLoyalty?.newVsReturning ?
                        data.visitorLoyalty.newVsReturning.new /
                        (data.visitorLoyalty.newVsReturning.new + data.visitorLoyalty.newVsReturning.returning || 1) : 0
                      )}
                    </div>
                    <div className="text-xs sm:text-sm font-medium text-blue-500">
                      {data?.visitorLoyalty?.newVsReturning ?
                        data.visitorLoyalty.newVsReturning.new.toLocaleString() : 0}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs sm:text-sm text-muted-foreground">Returning Visitors</div>
                    <div className="text-lg sm:text-xl font-medium">
                      {formatPercent(
                        data?.visitorLoyalty?.newVsReturning ?
                        data.visitorLoyalty.newVsReturning.returning /
                        (data.visitorLoyalty.newVsReturning.new + data.visitorLoyalty.newVsReturning.returning || 1) : 0
                      )}
                    </div>
                    <div className="text-xs sm:text-sm font-medium text-purple-500">
                      {data?.visitorLoyalty?.newVsReturning ?
                        data.visitorLoyalty.newVsReturning.returning.toLocaleString() : 0}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Visit Frequency */}
              <div>
                <h3 className="text-base sm:text-lg font-medium mb-3">Visit Frequency</h3>
                <div className="h-[200px] sm:h-[250px] w-full overflow-hidden min-w-0">
                  {visitFrequencyData.length > 0 ? (
                    <BarChart
                      data={visitFrequencyData}
                      index="visits"
                      categories={['count']}
                      colors={['#ec4899']}
                      valueFormatter={(value) => `${Number.isFinite(value) ? value.toLocaleString() : '0'} visitors`}
                      showAnimation={true}
                      showLegend={false}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-slate-50 dark:bg-slate-900/50 rounded-md">
                      <p className="text-muted-foreground">No visit frequency data available</p>
                    </div>
                  )}
                </div>
                <div className="p-4 bg-slate-50 dark:bg-slate-900/50 rounded-lg mt-4">
                  <div className="flex items-start gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500 mt-0.5" />
                    <div className="text-sm">
                      <span className="font-medium">
                        {formatPercent(
                          data?.visitorLoyalty?.newVsReturning ? 
                          data.visitorLoyalty.newVsReturning.returning / 
                          (data.visitorLoyalty.newVsReturning.new + data.visitorLoyalty.newVsReturning.returning || 1) : 0
                        )}
                      </span> of visitors return to your site, showing strong engagement with your content.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* Retention Tab - Mobile Optimized */}
          <TabsContent value="retention" className="space-y-6">
            {/* Overall Retention */}
            <div className="text-center p-4 sm:p-6 bg-slate-50 dark:bg-slate-900/50 rounded-lg">
              <h3 className="text-lg sm:text-xl font-medium text-primary mb-2">Overall Visitor Retention</h3>
              <div className="text-3xl sm:text-4xl font-bold">
                {formatPercent(data?.retention?.overall)}
              </div>
              <div className="mt-2 text-xs sm:text-sm text-muted-foreground">
                Percentage of visitors who return within 30 days
              </div>
              <div className="mt-4 flex items-center justify-center">
                {data?.retention?.overall && data.retention.overall > 0.5 ? (
                  <Badge variant="secondary" className="flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    Strong retention
                  </Badge>
                ) : data?.retention?.overall && data.retention.overall > 0.3 ? (
                  <Badge variant="outline" className="flex items-center gap-1.5">
                    <Activity className="h-3.5 w-3.5" />
                    Average retention
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="flex items-center gap-1.5">
                    <UserMinus className="h-3.5 w-3.5" />
                    Low retention
                  </Badge>
                )}
              </div>
            </div>
            
            {/* Retention by Week - Mobile Optimized */}
            <div>
              <h3 className="text-base sm:text-lg font-medium mb-3">Retention Rate by Week</h3>
              <div className="h-[200px] sm:h-[250px] w-full overflow-hidden min-w-0">
                {retentionData.length > 0 ? (
                  <LineChart
                    data={retentionData}
                    index="week"
                    categories={['rate']}
                    colors={['#10b981']}
                    valueFormatter={(value) => formatPercent(value)}
                    showAnimation={true}
                    showLegend={false}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full bg-slate-50 dark:bg-slate-900/50 rounded-md">
                    <p className="text-muted-foreground">No retention data available</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Retention Insights - Mobile Optimized */}
            <div className="p-3 sm:p-4 bg-slate-50 dark:bg-slate-900/50 rounded-lg">
              <h4 className="text-sm font-medium mb-2">Retention Insights</h4>
              <ul className="space-y-3 text-xs sm:text-sm">
                <li className="flex items-start gap-2">
                  <UserCheck className="h-4 w-4 text-green-500 mt-0.5" />
                  <span>
                    {formatPercent(data.retention.overall)} of visitors return within 30 days of their first visit
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Share2 className="h-4 w-4 text-blue-500 mt-0.5" />
                  <span>
                    Visitors who view 5+ pages are {formatPercent(0.8)} more likely to return
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Users className="h-4 w-4 text-purple-500 mt-0.5" />
                  <span>
                    {formatPercent(
                      data?.visitorLoyalty?.visitFrequency && 
                      Array.isArray(data.visitorLoyalty.visitFrequency) &&
                      data.visitorLoyalty.visitFrequency.length > 3 && 
                      data.visitorLoyalty.visitFrequency[3] &&
                      typeof data.visitorLoyalty.visitFrequency[3].count === 'number' ? 
                      (data.visitorLoyalty.visitFrequency[3].count / 
                        data.visitorLoyalty.visitFrequency.reduce((sum, item) => sum + (item?.count || 0), 0)) : 0
                    )} of visitors have returned 5+ times
                  </span>
                </li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 