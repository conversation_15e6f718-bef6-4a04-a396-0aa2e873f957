'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Edit,
  Trash2,
  Key,
  Clock,
  AlertCircle,
  XCircle,
  CheckCircle,
  Users,
  MoreHorizontal,
} from 'lucide-react';
import { Button } from '@/components/ui/button';

// Use the same User type as in the admin users page
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status?: string;
  verified?: boolean;
  createdAt?: string;
  [key: string]: any;
}

interface UserActionDialogProps {
  user: User;
  onEdit: (user: User) => void;
  onResetPassword: (id: string, name: string) => void;
  onRoleChange: (userId: string, newRole: "user" | "admin" | "superadmin") => Promise<void>;
  onStatusChange: (userId: string, newStatus: "active" | "inactive" | "pending" | "suspended") => Promise<void>;
  onDelete: (user: User) => void;
}

export function UserActionDialog({
  user,
  onEdit,
  onResetPassword,
  onRoleChange,
  onStatusChange,
  onDelete,
}: UserActionDialogProps) {
  const [open, setOpen] = useState(false);

  const handleAction = (action: () => void) => {
    action();
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-vista-light/10 focus-visible:ring-offset-0 focus-visible:ring-0 rounded-full h-8 w-8 md:h-9 md:w-9 transition-colors duration-200 opacity-70 hover:opacity-100 group-hover:opacity-100"
        >
          <MoreHorizontal className="h-4 w-4 md:h-5 md:w-5 text-vista-light" />
          <span className="sr-only">User actions</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="border border-vista-light/20 bg-vista-dark shadow-xl w-[90vw] max-w-md sm:max-w-lg rounded-lg overflow-hidden p-0">
        <DialogHeader className="border-b border-vista-light/10 p-4">
          <DialogTitle className="text-vista-light text-lg font-medium">
            Actions for {user.name}
          </DialogTitle>
        </DialogHeader>
        <div className="p-2 max-h-[70vh] overflow-y-auto">
          {/* Actions Section */}
          <div className="py-2">
            <h3 className="text-vista-light/90 text-sm font-medium px-3 py-2">Actions</h3>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onEdit(user))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <Edit className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>Edit User</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onResetPassword(user.id, user.name))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <Key className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>Reset Password</span>
            </Button>
          </div>

          {/* Change Role Section */}
          <div className="py-2 border-t border-vista-light/15">
            <h3 className="text-vista-light/90 text-sm font-medium px-3 py-2">Change Role</h3>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onRoleChange(user.id, 'user'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <span className="mr-2 w-4 h-4 flex-shrink-0"></span>
              <span>Make User</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onRoleChange(user.id, 'admin'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <Users className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>Make Admin</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onRoleChange(user.id, 'superadmin'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <span className="mr-2 w-4 h-4 flex-shrink-0"></span>
              <span>Make Super Admin</span>
            </Button>
          </div>

          {/* Status Section */}
          <div className="py-2 border-t border-vista-light/15">
            <h3 className="text-vista-light/90 text-sm font-medium px-3 py-2">Status</h3>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onStatusChange(user.id, 'active'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <CheckCircle className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
              <span>Set Active</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onStatusChange(user.id, 'inactive'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <XCircle className="mr-2 h-4 w-4 text-red-500 flex-shrink-0" />
              <span>Set Inactive</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onStatusChange(user.id, 'pending'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <Clock className="mr-2 h-4 w-4 text-yellow-500 flex-shrink-0" />
              <span>Set Pending</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onStatusChange(user.id, 'suspended'))}
              className="w-full justify-start hover:bg-vista-light/10 text-vista-light/90 cursor-pointer py-2 px-3 text-sm transition-colors duration-150 focus:bg-vista-light/10"
            >
              <AlertCircle className="mr-2 h-4 w-4 text-orange-500 flex-shrink-0" />
              <span>Set Suspended</span>
            </Button>
          </div>

          {/* Delete Section */}
          <div className="py-2 border-t border-vista-light/15">
            <Button
              variant="ghost"
              onClick={() => handleAction(() => onDelete(user))}
              className="w-full justify-start text-red-300 focus:text-red-300 hover:bg-red-500/20 focus:bg-red-500/20 cursor-pointer py-2 px-3 text-sm transition-colors duration-150"
            >
              <Trash2 className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>Delete User</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
