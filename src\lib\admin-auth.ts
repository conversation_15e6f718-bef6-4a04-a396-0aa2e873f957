import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';

/**
 * Get the admin user ID from cookies or query parameters
 * and verify that the user has admin privileges
 */
export async function getAdminUserId(request: NextRequest): Promise<{
  userId: string | null;
  isAuthorized: boolean;
  error?: NextResponse;
}> {
  try {

    // Get the user ID from the cookie or query parameter
    const { searchParams } = new URL(request.url);
    let userId = request.cookies.get('userId')?.value;

    // If no userId in cookies, try query string
    if (!userId) {
      userId = searchParams.get('userId') || '';
    }

    if (!userId) {
      return {
        userId: null,
        isAuthorized: false,
        error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      };
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).select('role').lean();

    if (!user) {
      return {
        userId,
        isAuthorized: false,
        error: NextResponse.json({ error: 'User not found' }, { status: 404 })
      };
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      return {
        userId,
        isAuthorized: false,
        error: NextResponse.json({ error: 'Forbidden: You do not have permission to access this resource.' }, { status: 403 })
      };
    }

    // User is authorized
    return { userId, isAuthorized: true };
  } catch (error) {
    console.error('Error in admin authentication:', error);

    return {
      userId: null,
      isAuthorized: false,
      error: NextResponse.json(
        { error: 'Server error during authorization', message: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      )
    };
  }
}

/**
 * Verify if a user has admin privileges from a request
 * @param request The Next.js request object
 * @returns An object with authentication details and user information
 */
export async function verifyAdmin(request: NextRequest) {
  // Get the userId from cookies first, then try query parameters as fallback
  let userId = request.cookies.get('userId')?.value;
  
  // If no userId in cookies, try query parameters (same as admin dashboard)
  if (!userId) {
    const { searchParams } = new URL(request.url);
    userId = searchParams.get('userId') || undefined;
  }
  
  try {
    console.log('verifyAdmin: Strict authentication required');
    console.log('verifyAdmin: userId from cookies:', request.cookies.get('userId')?.value);
    console.log('verifyAdmin: userId from query:', new URL(request.url).searchParams.get('userId'));

    // Check if the user is authenticated
    if (!userId) {
      console.log('verifyAdmin: No userId found in cookies or query parameters - authentication failed');
      return null;
    }

    console.log(`verifyAdmin: Attempting to validate userId: ${userId}`);

    // Connect to database
    try {
      await ensureMongooseConnection();
      console.log('verifyAdmin: Database connection successful');
    } catch (dbError) {
      console.error('verifyAdmin: Database connection failed:', dbError);
      return null;
    }

    // Import User model dynamically to avoid circular dependencies
    const { default: User } = await import('@/models/User');

    // Find user by ID
    const user = await User.findById(userId).select('email role name').lean();

    if (!user) {
      console.log(`verifyAdmin: User not found in database for ID ${userId}`);
      return null;
    }

    console.log(`verifyAdmin: User found - email: ${user.email}, role: ${user.role}`);

    // Check if user has admin role
    const isAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isAdmin) {
      console.log(`verifyAdmin: User ${userId} does not have admin role (current role: ${user.role})`);
      return null;
    }

    console.log(`verifyAdmin: Authentication successful for admin user ${user.email}`);
    return {
      isAuthorized: true,
      userId,
      user
    };
  } catch (error) {
    console.error('verifyAdmin: Unexpected error during authentication:', error);

    // Enhanced error logging
    console.error('verifyAdmin: Error details:', {
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      userId: userId || 'undefined',
      environment: process.env.NODE_ENV,
      netlify: process.env.NETLIFY,
      mongodbUri: process.env.MONGODB_URI ? 'defined' : 'undefined'
    });

    return null;
  }
}
